#!/bin/bash

echo "🛑 关闭 Celery Worker..."

# 查找 Celery 进程
CELERY_PIDS=$(pgrep -f "celery.*worker")

if [ -z "$CELERY_PIDS" ]; then
    echo "ℹ️  没有运行中的 Celery Worker"
    exit 0
fi

echo "📋 找到 Celery 进程: $CELERY_PIDS"

# 优雅关闭
echo "🔄 尝试优雅关闭..."
pkill -TERM -f "celery.*worker"

# 等待 5 秒
echo "⏳ 等待进程关闭..."
sleep 5

# 检查是否还有进程
REMAINING_PIDS=$(pgrep -f "celery.*worker")
if [ ! -z "$REMAINING_PIDS" ]; then
    echo "⚠️  强制关闭剩余进程: $REMAINING_PIDS"
    pkill -9 -f "celery.*worker"
    sleep 2
fi

# 最终检查
FINAL_PIDS=$(pgrep -f "celery.*worker")
if [ -z "$FINAL_PIDS" ]; then
    echo "✅ Celery Worker 已关闭"
else
    echo "❌ 仍有进程运行: $FINAL_PIDS"
    echo "💡 请手动关闭或重启终端"
fi
