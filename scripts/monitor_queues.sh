#!/bin/bash

echo "📊 Redis 队列监控"
echo "=================="

# Redis 配置
REDIS_HOST="***********"
REDIS_PORT="6379"
REDIS_PASSWORD="wtg2024@"

# 检查 Redis 连接
check_redis() {
    if redis-cli -h $REDIS_HOST -p $REDIS_PORT -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; then
        echo "✅ Redis 连接正常"
        return 0
    else
        echo "❌ Redis 连接失败"
        return 1
    fi
}

# 获取队列长度
get_queue_length() {
    local db=$1
    local queue_name=$2
    redis-cli -h $REDIS_HOST -p $REDIS_PORT -n $db -a "$REDIS_PASSWORD" LLEN $queue_name 2>/dev/null || echo "0"
}

# 显示队列状态
show_queue_status() {
    echo ""
    echo "📋 队列状态 ($(date '+%Y-%m-%d %H:%M:%S')):"
    echo "----------------------------------------"
    
    # Dev 环境
    local dev_length=$(get_queue_length 4 "protocol_parse_queue_dev")
    echo "🔵 DEV  环境 (DB 4): $dev_length 个任务"
    
    # Main 环境
    local main_length=$(get_queue_length 5 "protocol_parse_queue_main")
    echo "🔴 MAIN 环境 (DB 5): $main_length 个任务"
    
    # Local 环境
    local local_length=$(get_queue_length 6 "protocol_parse_queue_local")
    echo "🟡 LOCAL 环境 (DB 6): $local_length 个任务"
    
    echo "----------------------------------------"
    echo "📊 总计: $((dev_length + main_length + local_length)) 个任务"
}

# 显示 Celery Worker 状态
show_worker_status() {
    echo ""
    echo "👷 Celery Worker 状态:"
    echo "----------------------"
    
    local worker_count=$(pgrep -f "celery.*worker" | wc -l)
    if [ $worker_count -gt 0 ]; then
        echo "✅ 运行中的 Worker: $worker_count 个"
        echo "📋 进程列表:"
        pgrep -f "celery.*worker" | while read pid; do
            local cmd=$(ps -p $pid -o command= 2>/dev/null | cut -c1-80)
            echo "   PID $pid: $cmd..."
        done
    else
        echo "❌ 没有运行中的 Worker"
    fi
}

# 主函数
main() {
    # 检查参数
    if [ "$1" = "--watch" ] || [ "$1" = "-w" ]; then
        echo "🔄 实时监控模式 (按 Ctrl+C 退出)"
        echo "================================"
        
        if ! check_redis; then
            exit 1
        fi
        
        while true; do
            clear
            echo "📊 Redis 队列实时监控"
            echo "======================"
            show_queue_status
            show_worker_status
            echo ""
            echo "🔄 每 3 秒刷新一次，按 Ctrl+C 退出"
            sleep 3
        done
    else
        # 单次检查
        if check_redis; then
            show_queue_status
            show_worker_status
        fi
    fi
}

# 显示帮助
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Redis 队列监控脚本"
    echo ""
    echo "用法:"
    echo "  $0           # 单次检查队列状态"
    echo "  $0 --watch   # 实时监控模式"
    echo "  $0 --help    # 显示帮助"
    echo ""
    echo "监控的队列:"
    echo "  - protocol_parse_queue_dev   (DB 4)"
    echo "  - protocol_parse_queue_main  (DB 5)"
    echo "  - protocol_parse_queue_local (DB 6)"
    exit 0
fi

# 运行主函数
main "$@"
