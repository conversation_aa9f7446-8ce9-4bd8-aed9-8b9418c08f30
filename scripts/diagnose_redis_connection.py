#!/usr/bin/env python3
"""
Redis 连接诊断脚本

用于诊断和解决 Redis 连接问题
"""

import os
import sys
import time
import redis
import socket
import subprocess
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings


def test_network_connectivity():
    """测试网络连通性"""
    print("🌐 网络连通性测试")
    print("=" * 50)
    
    host = settings.REDIS_HOST
    port = settings.REDIS_PORT
    
    # 1. Ping 测试
    print(f"1. Ping 测试 {host}...")
    try:
        result = subprocess.run(['ping', '-c', '3', host], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ping 成功")
            # 提取延迟信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'time=' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ Ping 失败")
            print(f"   错误: {result.stderr}")
    except Exception as e:
        print(f"❌ Ping 测试异常: {e}")
    
    print()
    
    # 2. TCP 连接测试
    print(f"2. TCP 连接测试 {host}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        start_time = time.time()
        result = sock.connect_ex((host, port))
        end_time = time.time()
        sock.close()
        
        if result == 0:
            print(f"✅ TCP 连接成功 (耗时: {(end_time - start_time)*1000:.2f}ms)")
        else:
            print(f"❌ TCP 连接失败 (错误码: {result})")
    except Exception as e:
        print(f"❌ TCP 连接异常: {e}")
    
    print()


def test_redis_connection():
    """测试 Redis 连接"""
    print("🔧 Redis 连接测试")
    print("=" * 50)
    
    configs = [
        {
            "name": "基础连接",
            "config": {
                "host": settings.REDIS_HOST,
                "port": settings.REDIS_PORT,
                "password": settings.REDIS_PASSWORD,
                "db": settings.REDIS_DB,
            }
        },
        {
            "name": "优化连接",
            "config": {
                "host": settings.REDIS_HOST,
                "port": settings.REDIS_PORT,
                "password": settings.REDIS_PASSWORD,
                "db": settings.REDIS_DB,
                "socket_connect_timeout": 20,
                "socket_timeout": 60,
                "socket_keepalive": True,
                "socket_keepalive_options": {
                    "TCP_KEEPIDLE": 60,
                    "TCP_KEEPINTVL": 30,
                    "TCP_KEEPCNT": 3,
                }
            }
        }
    ]
    
    for i, test_config in enumerate(configs, 1):
        print(f"{i}. {test_config['name']}测试...")
        try:
            start_time = time.time()
            r = redis.Redis(**test_config['config'])
            
            # 测试连接
            r.ping()
            connect_time = time.time() - start_time
            print(f"   ✅ 连接成功 (耗时: {connect_time*1000:.2f}ms)")
            
            # 测试基本操作
            test_key = f"test_connection_{int(time.time())}"
            r.set(test_key, "test_value", ex=10)
            value = r.get(test_key)
            r.delete(test_key)
            
            if value == b"test_value":
                print("   ✅ 读写操作正常")
            else:
                print("   ❌ 读写操作异常")
            
            # 测试队列操作
            queue_name = settings.CELERY_QUEUE_NAME
            queue_len = r.llen(queue_name)
            print(f"   ✅ 队列 '{queue_name}' 长度: {queue_len}")
            
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
        
        print()


def test_connection_stability():
    """测试连接稳定性"""
    print("⏱️ 连接稳定性测试")
    print("=" * 50)
    
    try:
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            socket_connect_timeout=20,
            socket_timeout=60,
            socket_keepalive=True,
            socket_keepalive_options={
                "TCP_KEEPIDLE": 60,
                "TCP_KEEPINTVL": 30,
                "TCP_KEEPCNT": 3,
            }
        )
        
        print("开始 30 秒连接稳定性测试...")
        print("(每 5 秒测试一次，按 Ctrl+C 提前结束)")
        
        for i in range(6):  # 30秒，每5秒一次
            try:
                start_time = time.time()
                r.ping()
                ping_time = (time.time() - start_time) * 1000
                
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"[{timestamp}] ✅ Ping 成功 ({ping_time:.2f}ms)")
                
                if i < 5:  # 最后一次不等待
                    time.sleep(5)
                    
            except KeyboardInterrupt:
                print("\n⚠️ 用户中断测试")
                break
            except Exception as e:
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"[{timestamp}] ❌ Ping 失败: {e}")
        
        print("✅ 连接稳定性测试完成")
        
    except Exception as e:
        print(f"❌ 稳定性测试失败: {e}")
    
    print()


def show_recommendations():
    """显示优化建议"""
    print("💡 优化建议")
    print("=" * 50)
    
    print("如果经常出现连接中断，可以尝试以下方法：")
    print()
    print("1. 网络优化:")
    print("   - 使用有线网络而非 WiFi")
    print("   - 检查防火墙设置")
    print("   - 考虑使用 VPN 稳定连接")
    print()
    print("2. Redis 配置优化:")
    print("   - 增加连接超时时间")
    print("   - 启用 TCP keepalive")
    print("   - 增加重试次数和间隔")
    print()
    print("3. Celery 配置优化:")
    print("   - 使用本地开发专用配置")
    print("   - 增加心跳间隔")
    print("   - 启用自动重连")
    print()
    print("4. 本地 Redis 服务:")
    print("   - 考虑在本地运行 Redis 服务器")
    print("   - 使用 Docker 运行 Redis")
    print("   - 配置 Redis 代理")


def main():
    print("🔍 Redis 连接诊断工具")
    print("=" * 60)
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"环境: {settings.CELERY_ENV}")
    print(f"Redis: {settings.REDIS_HOST}:{settings.REDIS_PORT} DB{settings.REDIS_DB}")
    print(f"队列: {settings.CELERY_QUEUE_NAME}")
    print("=" * 60)
    print()
    
    # 执行诊断
    test_network_connectivity()
    test_redis_connection()
    
    # 询问是否进行稳定性测试
    try:
        response = input("是否进行连接稳定性测试？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            test_connection_stability()
    except KeyboardInterrupt:
        print("\n")
    
    show_recommendations()
    
    print("=" * 60)
    print("🎯 诊断完成！")


if __name__ == "__main__":
    main()
