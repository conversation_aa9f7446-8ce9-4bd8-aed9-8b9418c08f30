#!/usr/bin/env python3
"""
环境隔离测试脚本

模拟不同环境的任务提交，验证任务是否正确隔离
"""

import os
import sys
import time
import json
import redis
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings


def get_redis_client(db_num: int):
    """获取 Redis 客户端"""
    if settings.REDIS_PASSWORD:
        return redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=db_num,
            password=settings.REDIS_PASSWORD,
            decode_responses=True
        )
    else:
        return redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=db_num,
            decode_responses=True
        )


def check_queue_status():
    """检查各环境队列状态"""
    print("=" * 60)
    print("队列状态检查")
    print("=" * 60)
    
    environments = {
        "dev": 4,
        "main": 5, 
        "local": 6
    }
    
    for env, db_num in environments.items():
        try:
            r = get_redis_client(db_num)
            queue_name = f"protocol_parse_queue_{env}"
            
            # 检查队列长度
            queue_length = r.llen(queue_name)
            
            # 检查是否有 worker 连接
            # 注意：这个检查可能需要根据实际的 Celery 配置调整
            
            print(f"{env.upper()} 环境 (DB {db_num}):")
            print(f"  队列名: {queue_name}")
            print(f"  队列长度: {queue_length}")
            
            if env == settings.CELERY_ENV:
                print(f"  ✅ 当前环境")
            else:
                print(f"  🔒 其他环境")
            print()
            
        except Exception as e:
            print(f"❌ {env.upper()} 环境检查失败: {e}")
            print()


def simulate_task_submission():
    """模拟任务提交（仅用于测试队列隔离）"""
    print("=" * 60)
    print("模拟任务提交测试")
    print("=" * 60)
    
    try:
        from celery_task.celery import celery_app
        
        # 显示当前 Celery 配置
        print(f"当前环境: {settings.CELERY_ENV}")
        print(f"Celery 应用: {celery_app.main}")
        print(f"默认队列: {celery_app.conf.task_default_queue}")
        print()

        # 创建一个测试任务（不实际执行，只是放入队列）
        test_file_key = f"test/isolation-test-{settings.CELERY_ENV}-{int(time.time())}.docx"
        
        print(f"模拟提交任务: {test_file_key}")
        print("注意：这只是测试队列隔离，不会实际处理文件")
        print()
        
        # 检查任务是否进入正确的队列
        r = get_redis_client(settings.REDIS_DB)
        queue_name = settings.CELERY_QUEUE_NAME
        
        before_length = r.llen(queue_name)
        print(f"提交前队列长度: {before_length}")
        
        # 这里可以添加实际的任务提交代码
        # task = extract_protocol_task.delay(test_file_key, False)
        # print(f"任务 ID: {task.id}")
        
        print("✅ 队列隔离测试完成")
        
    except Exception as e:
        print(f"❌ 任务提交测试失败: {e}")


def generate_monitoring_commands():
    """生成监控命令"""
    print("=" * 60)
    print("监控命令")
    print("=" * 60)
    
    environments = {
        "dev": 4,
        "main": 5,
        "local": 6
    }
    
    print("Redis 队列监控命令:")
    for env, db_num in environments.items():
        queue_name = f"protocol_parse_queue_{env}"
        cmd = f'redis-cli -h {settings.REDIS_HOST} -p {settings.REDIS_PORT} -n {db_num} -a "{settings.REDIS_PASSWORD}" LLEN {queue_name}'
        print(f"# {env.upper()} 环境")
        print(f"{cmd}")
        print()
    
    print("实时监控脚本:")
    print("watch -n 2 'echo \"=== 队列状态 ===\" && \\")
    for env, db_num in environments.items():
        queue_name = f"protocol_parse_queue_{env}"
        cmd = f'redis-cli -h {settings.REDIS_HOST} -p {settings.REDIS_PORT} -n {db_num} -a "{settings.REDIS_PASSWORD}" LLEN {queue_name}'
        print(f'echo "{env.upper()}: $({cmd})" && \\')
    print('echo "完成"\'')


if __name__ == "__main__":
    print(f"环境隔离测试 - 当前环境: {settings.CELERY_ENV}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查队列状态
    check_queue_status()
    
    # 模拟任务提交
    simulate_task_submission()
    
    # 生成监控命令
    generate_monitoring_commands()
    
    print("=" * 60)
    print("测试完成！")
    print()
    print("下一步:")
    print("1. 在不同环境部署应用")
    print("2. 使用上面的监控命令检查队列隔离")
    print("3. 发送实际请求验证任务不会跨环境执行")
