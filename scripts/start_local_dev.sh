#!/bin/bash

# 本地开发环境启动脚本

echo "🚀 启动本地开发环境..."

# 检查是否在项目根目录
if [ ! -f "app.py" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 激活虚拟环境
if [ -d ".venv" ]; then
    source .venv/bin/activate
    echo "✅ 虚拟环境已激活"
else
    echo "❌ 未找到虚拟环境，请先创建: python -m venv .venv"
    exit 1
fi

# 设置环境变量
export CELERY_ENV=local
export REDIS_DB=6
export REDIS_TASK_DB=6
export DISABLE_NACOS=true
export NACOS_ENABLE_IN_CELERY=false
export EXPOSE_TASK_TRACEBACK=true
export FAIL_FAST_DOCX_CHECK=true
export ENABLE_DOC_CONVERSION=false

echo "✅ 环境变量已设置："
echo "   CELERY_ENV: $CELERY_ENV"
echo "   REDIS_DB: $REDIS_DB"
echo "   REDIS_TASK_DB: $REDIS_TASK_DB"
echo "   DISABLE_NACOS: $DISABLE_NACOS"

# 验证配置
echo ""
echo "🔍 验证环境配置..."
if python scripts/verify_environment_isolation.py; then
    echo ""
    echo "🎉 本地开发环境准备完成！"
    echo ""
    echo "下一步："
    echo "1. 启动 Celery Worker: ./scripts/start_celery.sh"
    echo "2. 启动 FastAPI 应用: ./scripts/start_app.sh"
    echo "3. 访问应用文档: http://localhost:8000/docs"
    echo ""
    echo "💡 提示："
    echo "- 使用 ./scripts/stop_celery.sh 关闭 Celery"
    echo "- 使用 Ctrl+C 关闭 FastAPI 应用"
    echo "- 使用 deactivate 退出虚拟环境"
else
    echo ""
    echo "❌ 环境配置验证失败，请检查配置"
    exit 1
fi
