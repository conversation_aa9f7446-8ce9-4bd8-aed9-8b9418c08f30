#!/bin/bash

echo "🚀 启动 FastAPI 应用..."

# 检查是否在项目根目录
if [ ! -f "app.py" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 激活虚拟环境
if [ -d ".venv" ]; then
    source .venv/bin/activate
    echo "✅ 虚拟环境已激活"
else
    echo "❌ 未找到虚拟环境，请先运行: ./scripts/start_local_dev.sh"
    exit 1
fi

# 设置环境变量
export CELERY_ENV=${CELERY_ENV:-local}
export REDIS_DB=${REDIS_DB:-6}
export REDIS_TASK_DB=${REDIS_TASK_DB:-6}
export DISABLE_NACOS=${DISABLE_NACOS:-true}
export NACOS_ENABLE_IN_CELERY=${NACOS_ENABLE_IN_CELERY:-false}

echo "📋 应用配置："
echo "   环境: $CELERY_ENV"
echo "   端口: 8000"
echo "   Redis DB: $REDIS_DB"
echo "   主机: 0.0.0.0"

# 检查端口是否被占用
if lsof -i :8000 >/dev/null 2>&1; then
    echo "⚠️  端口 8000 已被占用"
    echo "🔍 占用进程："
    lsof -i :8000
    echo ""
    read -p "是否要杀死占用进程并继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔄 关闭占用进程..."
        lsof -ti :8000 | xargs kill -9
        sleep 2
    else
        echo "❌ 取消启动"
        exit 1
    fi
fi

echo ""
echo "🚀 启动 FastAPI 应用..."
echo "📖 API 文档: http://localhost:8000/docs"
echo "🔧 ReDoc 文档: http://localhost:8000/redoc"
echo "💡 按 Ctrl+C 停止应用"
echo ""

# 启动应用
uvicorn app:app --reload --host 0.0.0.0 --port 8000
