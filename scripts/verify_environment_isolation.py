#!/usr/bin/env python3
"""
环境隔离验证脚本

用于验证不同环境的 Celery 配置是否正确隔离
"""

import os
import sys
import redis
from urllib.parse import quote_plus

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings


def test_redis_connection(db_num: int, description: str):
    """测试 Redis 连接"""
    try:
        # 构建 Redis 连接
        if settings.REDIS_PASSWORD:
            r = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=db_num,
                password=settings.REDIS_PASSWORD,
                decode_responses=True
            )
        else:
            r = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=db_num,
                decode_responses=True
            )
        
        # 测试连接
        r.ping()
        print(f"✅ {description} Redis DB {db_num} 连接成功")
        
        # 检查队列
        queue_name = f"protocol_parse_queue_{settings.APP_ENV}"
        queue_length = r.llen(queue_name)
        print(f"   队列 '{queue_name}' 长度: {queue_length}")
        
        return True
        
    except Exception as e:
        print(f"❌ {description} Redis DB {db_num} 连接失败: {e}")
        return False


def verify_environment_config():
    """验证环境配置"""
    print("=" * 60)
    print("环境隔离配置验证")
    print("=" * 60)
    
    # 显示当前配置
    print(f"当前环境: {settings.APP_ENV}")
    print(f"Redis 主机: {settings.REDIS_HOST}:{settings.REDIS_PORT}")
    print(f"Broker DB: {settings.REDIS_DB}")
    print(f"Result DB: {settings.REDIS_TASK_DB}")
    print(f"Celery 应用名: {settings.CELERY_APP_NAME}")
    print(f"队列名: {settings.CELERY_QUEUE_NAME}")
    print()
    
    # 测试 Redis 连接
    print("测试 Redis 连接:")
    broker_ok = test_redis_connection(settings.REDIS_DB, "Broker")
    result_ok = test_redis_connection(settings.REDIS_TASK_DB, "Result")
    print()
    
    # 检查环境隔离
    print("环境隔离检查:")
    environments = ["dev", "main", "local"]
    db_mapping = {"dev": 4, "main": 5, "local": 6}
    
    for env in environments:
        if env == settings.APP_ENV:
            print(f"✅ 当前环境: {env} (DB {db_mapping[env]})")
        else:
            print(f"🔒 其他环境: {env} (DB {db_mapping[env]}) - 已隔离")
    
    print()
    
    # 总结
    if broker_ok and result_ok:
        print("✅ 环境配置验证通过！")
        print(f"✅ {settings.APP_ENV} 环境已正确隔离")
        return True
    else:
        print("❌ 环境配置验证失败！")
        return False


if __name__ == "__main__":
    print("开始验证环境隔离配置...")
    print()
    
    # 显示环境变量
    print("相关环境变量:")
    env_vars = ["APP_ENV", "REDIS_HOST", "REDIS_PORT", "REDIS_DB", "REDIS_TASK_DB"]
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"  {var}: {value}")
    print()
    
    # 执行验证
    config_ok = verify_environment_config()
    
    print("=" * 60)
    if config_ok:
        print("🎉 环境隔离配置验证通过！")
        sys.exit(0)
    else:
        print("💥 验证失败！请检查配置。")
        sys.exit(1)
