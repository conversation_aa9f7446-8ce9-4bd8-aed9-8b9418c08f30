#!/bin/bash

echo "🔧 启动 Celery Worker..."

# 检查是否在项目根目录
if [ ! -f "app.py" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 激活虚拟环境
if [ -d ".venv" ]; then
    source .venv/bin/activate
    echo "✅ 虚拟环境已激活"
else
    echo "❌ 未找到虚拟环境，请先运行: ./scripts/start_local_dev.sh"
    exit 1
fi

# 设置环境变量（如果未设置）
export CELERY_ENV=${CELERY_ENV:-local}
export REDIS_DB=${REDIS_DB:-6}
export REDIS_TASK_DB=${REDIS_TASK_DB:-6}
export DISABLE_NACOS=${DISABLE_NACOS:-true}
export NACOS_ENABLE_IN_CELERY=${NACOS_ENABLE_IN_CELERY:-false}

echo "📋 Celery 配置："
echo "   环境: $CELERY_ENV"
echo "   Redis DB: $REDIS_DB"
echo "   队列: protocol_parse_queue_$CELERY_ENV"
echo "   主机名: local-worker@$(hostname)"

# 检查 Redis 连接
echo ""
echo "🔍 检查 Redis 连接..."
if python -c "
import redis
try:
    r = redis.Redis(host='***********', port=6379, db=$REDIS_DB, password='wtg2024@')
    r.ping()
    print('✅ Redis 连接成功')
except Exception as e:
    print(f'❌ Redis 连接失败: {e}')
    exit(1)
"; then
    echo ""
    echo "🚀 启动 Celery Worker..."
    echo "💡 按 Ctrl+C 停止 Worker"
    echo ""
    
    # 启动 Celery Worker
    celery -A celery_task worker \
        --loglevel=info \
        --pool=prefork \
        --concurrency=2 \
        --hostname="local-worker@%h" \
        --time-limit=300 \
        --soft-time-limit=240 \
        --max-tasks-per-child=10 \
        --prefetch-multiplier=1 \
        --without-gossip \
        --without-mingle \
        --without-heartbeat
else
    echo "❌ 无法启动 Celery Worker，请检查 Redis 连接"
    exit 1
fi
