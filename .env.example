# 本地开发环境配置模板
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 环境配置
# ================================
# 环境标识：local, dev, main
CELERY_ENV=local

# ================================
# Redis 配置
# ================================
# Redis 主机地址
REDIS_HOST=***********
# Redis 端口
REDIS_PORT=6379
# Redis 密码
REDIS_PASSWORD=wtg2024@

# Redis 数据库编号（环境隔离关键配置）
# local: 6, dev: 4, main: 5
REDIS_DB=6
REDIS_TASK_DB=6

# ================================
# Nacos 配置（本地开发建议禁用）
# ================================
# 是否禁用 Nacos
DISABLE_NACOS=true
# 是否在 Celery 中启用 Nacos
NACOS_ENABLE_IN_CELERY=false

# Nacos 服务器配置（如果启用）
# NACOS_SERVER=your-nacos-server
# NACOS_NAMESPACE=your-namespace
# NACOS_TIMEOUT=10
# NACOS_MAX_RETRIES=3
# NACOS_RETRY_DELAY=5
# NACOS_INITIAL_DELAY=3

# ================================
# 任务配置
# ================================
# 任务结果过期时间（秒）
TASK_RESULT_EXPIRE_TIME=86400
# 最大并发任务数
MAX_CONCURRENT_TASKS=5
# 任务超时时间（秒）
TASK_TIMEOUT=1800
# 最大重试次数
MAX_RETRY_TIMES=3

# ================================
# 调试配置
# ================================
# 是否暴露任务堆栈跟踪
EXPOSE_TASK_TRACEBACK=true
# 是否启用快速失败检查
FAIL_FAST_DOCX_CHECK=true
# 是否启用 DOC 转换
ENABLE_DOC_CONVERSION=false

# ================================
# 日志配置
# ================================
# 应用日志目录
APP_LOG_DIR=logs
# 应用阶段
APP_STAGE=local
