# 环境隔离解决方案

## 问题描述

之前 dev 和 main 环境共享同一个 Redis 实例和队列，导致：
- dev 环境的请求可能被 main 环境的 Celery Worker 处理
- main 环境的请求可能被 dev 环境的 Celery Worker 处理
- 调试时需要同时查看两个环境的日志

## 解决方案

通过 K8s 环境变量注入不同的 Redis DB 和队列名，实现环境完全隔离。

### 核心机制

| 环境 | CELERY_ENV | REDIS_DB | 队列名 | Celery 应用名 |
|------|---------|----------|--------|---------------|
| Dev | `dev` | 4 | `protocol_parse_queue_dev` | `protocol-parse-app-dev` |
| Main | `main` | 5 | `protocol_parse_queue_main` | `protocol-parse-app-main` |
| Local | `local` | 6 | `protocol_parse_queue_local` | `protocol-parse-app-local` |

## 快速开始

### 1. K8s 配置

#### Dev 环境
```yaml
env:
- name: CELERY_ENV
  value: "dev"
- name: REDIS_DB
  value: "4"
- name: REDIS_TASK_DB
  value: "4"
```

#### Main 环境
```yaml
env:
- name: CELERY_ENV
  value: "main"
- name: REDIS_DB
  value: "5"
- name: REDIS_TASK_DB
  value: "5"
```

### 2. 验证配置

```bash
# 在容器内运行
python scripts/verify_environment_isolation.py
```

### 3. 监控队列

```bash
# Dev 环境
redis-cli -h *********** -p 6379 -n 4 -a "wtg2024@" LLEN protocol_parse_queue_dev

# Main 环境
redis-cli -h *********** -p 6379 -n 5 -a "wtg2024@" LLEN protocol_parse_queue_main
```

## 文件说明

### 修改的文件
- `config/settings.py` - 添加环境变量支持
- `celery_task/celery.py` - 环境相关应用名
- `celery_task/celeryconfig.py` - 环境相关队列名
- `APP-META/docker-config/environment/common/app/conf/circus.ini` - 环境变量配置

### 新增的文件
- `docs/environment-isolation.md` - 详细说明文档
- `docs/deployment-guide.md` - 部署指南
- `scripts/verify_environment_isolation.py` - 配置验证脚本
- `scripts/test_environment_isolation.py` - 隔离测试脚本
- `k8s-examples/environment-isolation.yaml` - K8s 配置示例

## 兼容性

- **向后兼容**：不设置环境变量时，默认使用 dev 环境配置
- **渐进部署**：可以先在一个环境启用，验证后再推广
- **零停机**：部署过程不影响现有服务

## 验证效果

部署后，你会看到：

1. **启动日志**：
   ```
   加载 Celery 配置 - 环境: dev, 队列: protocol_parse_queue_dev
   ```

2. **队列隔离**：
   - dev 请求只进入 `protocol_parse_queue_dev`
   - main 请求只进入 `protocol_parse_queue_main`

3. **Worker 隔离**：
   - dev Worker 只处理 dev 队列的任务
   - main Worker 只处理 main 队列的任务

## 下一步

1. 在 dev 环境部署并验证
2. 在 main 环境部署并验证
3. 发送实际请求测试隔离效果
4. 更新监控和告警配置

---

**问题解决！** 🎉

现在 dev 和 main 环境的 Celery 任务完全隔离，不会再出现跨环境执行的问题。
