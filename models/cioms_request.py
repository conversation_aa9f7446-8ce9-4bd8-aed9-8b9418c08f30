#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CIOMS报告生成请求模型
"""

from typing import Optional
from pydantic import BaseModel, Field


class CiomsGenerateRequest(BaseModel):
    """CIOMS报告生成请求"""
    
    json_data: str = Field(..., description="JSON格式的CIOMS数据字符串")
    company_name: str = Field(..., description="公司名称，用于选择对应的模板")
    
    class Config:
        schema_extra = {
            "example": {
                "json_data": '{"report_id": "CIOMS-001", "reaction_information": {"patient_initials": "J.D."}}',
                "company_name": "b<PERSON><PERSON><PERSON>"
            }
        }


class CiomsGenerateResponse(BaseModel):
    """CIOMS报告生成响应"""
    
    file_url: str = Field(..., description="生成的CIOMS报告文件下载链接")
    file_key: str = Field(..., description="OSS文件键")
    file_name: str = Field(..., description="生成的文件名")
    
    class Config:
        schema_extra = {
            "example": {
                "file_url": "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/cioms/20250905/cioms_report_20250905_123456.docx",
                "file_key": "cioms/20250905/cioms_report_20250905_123456.docx",
                "file_name": "cioms_report_20250905_123456.docx"
            }
        }
