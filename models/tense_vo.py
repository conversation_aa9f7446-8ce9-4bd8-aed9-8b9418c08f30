from typing import Any, List

from pydantic import BaseModel, Field


class TenseConvertRequest(BaseModel):
    text: str = Field(..., title="输入英文文本", description="需要进行将来时 -> 过去时 转换的文本")

class TenseSentenceResult(BaseModel):
    original_text: str = Field(..., title="原句")
    target_text: str = Field(..., title="转换后的句子")
    is_future_tense: bool = Field(..., title="是否检测为将来时")
    convertInfos: list = Field([], title="具体的转换信息")


class TenseConvertResponse(BaseModel):
    code: int = Field(...)
    success: bool = Field(False)
    message: str | None = Field(None)
    result: TenseSentenceResult | Any = Field(None)
    time_cost: int | None = Field(None)


