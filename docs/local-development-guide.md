# Mac 本地开发环境指南

## 环境准备

### 1. 激活虚拟环境
```bash
cd /Users/<USER>/PycharmProjects/yiya-ai-bot
source .venv/bin/activate
```

### 2. 安装依赖（如果需要）
```bash
uv sync
```

## 环境变量配置

### 方式一：临时设置（推荐用于测试）

```bash
# 设置本地开发环境
export APP_ENV=local
export REDIS_DB=6
export REDIS_TASK_DB=6

# 可选：覆盖其他配置
export REDIS_HOST=***********
export REDIS_PORT=6379
export REDIS_PASSWORD=wtg2024@

# 禁用 Nacos（本地开发通常不需要）
export DISABLE_NACOS=true
export NACOS_ENABLE_IN_CELERY=false
```

### 方式二：使用 .env 文件（推荐用于日常开发）

创建 `.env` 文件：
```bash
# 在项目根目录创建 .env 文件
cat > .env << 'EOF'
# 本地开发环境配置
APP_ENV=local
REDIS_DB=6
REDIS_TASK_DB=6

# Redis 配置（可选，使用默认值）
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=wtg2024@

# Nacos 配置（本地开发建议禁用）
DISABLE_NACOS=true
NACOS_ENABLE_IN_CELERY=false

# 任务配置
TASK_RESULT_EXPIRE_TIME=86400
MAX_CONCURRENT_TASKS=5
TASK_TIMEOUT=1800
MAX_RETRY_TIMES=3

# 调试配置
EXPOSE_TASK_TRACEBACK=true
FAIL_FAST_DOCX_CHECK=true
ENABLE_DOC_CONVERSION=false
EOF
```

然后加载环境变量：
```bash
# 加载 .env 文件
set -a && source .env && set +a
```

### 方式三：使用脚本（最方便）

创建启动脚本：
```bash
cat > scripts/start_local_dev.sh << 'EOF'
#!/bin/bash

# 本地开发环境启动脚本

echo "🚀 启动本地开发环境..."

# 激活虚拟环境
source .venv/bin/activate

# 设置环境变量
export APP_ENV=local
export REDIS_DB=6
export REDIS_TASK_DB=6
export DISABLE_NACOS=true
export NACOS_ENABLE_IN_CELERY=false
export EXPOSE_TASK_TRACEBACK=true

echo "✅ 环境变量已设置："
echo "   APP_ENV: $APP_ENV"
echo "   REDIS_DB: $REDIS_DB"
echo "   REDIS_TASK_DB: $REDIS_TASK_DB"

# 验证配置
echo "🔍 验证环境配置..."
python scripts/verify_environment_isolation.py

echo "🎉 本地开发环境准备完成！"
echo ""
echo "下一步："
echo "1. 启动 Celery Worker: ./scripts/start_celery.sh"
echo "2. 启动 FastAPI 应用: uvicorn app:app --reload --host 0.0.0.0 --port 8000"
EOF

chmod +x scripts/start_local_dev.sh
```

## Celery 操作

### 1. 启动 Celery Worker

#### 方式一：直接启动
```bash
# 确保已设置环境变量
source .venv/bin/activate
export APP_ENV=local REDIS_DB=6 REDIS_TASK_DB=6

# 启动 Celery Worker
celery -A celery_task worker --loglevel=info --pool=prefork --concurrency=2
```

#### 方式二：使用启动脚本
```bash
cat > scripts/start_celery.sh << 'EOF'
#!/bin/bash

echo "🔧 启动 Celery Worker..."

# 激活虚拟环境
source .venv/bin/activate

# 设置环境变量（如果未设置）
export APP_ENV=${APP_ENV:-local}
export REDIS_DB=${REDIS_DB:-6}
export REDIS_TASK_DB=${REDIS_TASK_DB:-6}
export DISABLE_NACOS=${DISABLE_NACOS:-true}

echo "📋 Celery 配置："
echo "   环境: $APP_ENV"
echo "   Redis DB: $REDIS_DB"
echo "   队列: protocol_parse_queue_$APP_ENV"

# 启动 Celery Worker
celery -A celery_task worker \
    --loglevel=info \
    --pool=prefork \
    --concurrency=2 \
    --hostname="local-worker@%h" \
    --time-limit=300 \
    --soft-time-limit=240 \
    --max-tasks-per-child=10 \
    --prefetch-multiplier=1
EOF

chmod +x scripts/start_celery.sh

# 启动
./scripts/start_celery.sh
```

### 2. 关闭 Celery Worker

#### 方式一：优雅关闭
```bash
# 在 Celery Worker 运行的终端按 Ctrl+C
# 或者发送 SIGTERM 信号
pkill -f "celery.*worker"
```

#### 方式二：强制关闭
```bash
# 强制杀死所有 Celery 进程
pkill -9 -f "celery.*worker"
```

#### 方式三：使用脚本
```bash
cat > scripts/stop_celery.sh << 'EOF'
#!/bin/bash

echo "🛑 关闭 Celery Worker..."

# 查找 Celery 进程
CELERY_PIDS=$(pgrep -f "celery.*worker")

if [ -z "$CELERY_PIDS" ]; then
    echo "ℹ️  没有运行中的 Celery Worker"
    exit 0
fi

echo "📋 找到 Celery 进程: $CELERY_PIDS"

# 优雅关闭
echo "🔄 尝试优雅关闭..."
pkill -TERM -f "celery.*worker"

# 等待 5 秒
sleep 5

# 检查是否还有进程
REMAINING_PIDS=$(pgrep -f "celery.*worker")
if [ ! -z "$REMAINING_PIDS" ]; then
    echo "⚠️  强制关闭剩余进程: $REMAINING_PIDS"
    pkill -9 -f "celery.*worker"
fi

echo "✅ Celery Worker 已关闭"
EOF

chmod +x scripts/stop_celery.sh

# 使用
./scripts/stop_celery.sh
```

### 3. 重启 Celery Worker

```bash
cat > scripts/restart_celery.sh << 'EOF'
#!/bin/bash

echo "🔄 重启 Celery Worker..."

# 关闭现有 Worker
./scripts/stop_celery.sh

# 等待 2 秒
sleep 2

# 启动新 Worker
./scripts/start_celery.sh
EOF

chmod +x scripts/restart_celery.sh

# 使用
./scripts/restart_celery.sh
```

## FastAPI 应用操作

### 1. 启动 FastAPI 应用

```bash
# 确保已设置环境变量
source .venv/bin/activate
export APP_ENV=local REDIS_DB=6 REDIS_TASK_DB=6

# 启动应用（开发模式）
uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### 2. 创建完整启动脚本

```bash
cat > scripts/start_app.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 FastAPI 应用..."

# 激活虚拟环境
source .venv/bin/activate

# 设置环境变量
export APP_ENV=${APP_ENV:-local}
export REDIS_DB=${REDIS_DB:-6}
export REDIS_TASK_DB=${REDIS_TASK_DB:-6}
export DISABLE_NACOS=${DISABLE_NACOS:-true}

echo "📋 应用配置："
echo "   环境: $APP_ENV"
echo "   端口: 8000"
echo "   Redis DB: $REDIS_DB"

# 启动应用
uvicorn app:app --reload --host 0.0.0.0 --port 8000
EOF

chmod +x scripts/start_app.sh

# 使用
./scripts/start_app.sh
```

## 监控和调试

### 1. 检查环境配置

```bash
# 验证环境隔离配置
python scripts/verify_environment_isolation.py

# 测试环境隔离
python scripts/test_environment_isolation.py
```

### 2. 监控队列状态

```bash
# 实时监控本地队列
watch -n 2 'redis-cli -h *********** -p 6379 -n 6 -a "wtg2024@" LLEN protocol_parse_queue_local'

# 检查所有环境队列
redis-cli -h *********** -p 6379 -n 4 -a "wtg2024@" LLEN protocol_parse_queue_dev
redis-cli -h *********** -p 6379 -n 5 -a "wtg2024@" LLEN protocol_parse_queue_main  
redis-cli -h *********** -p 6379 -n 6 -a "wtg2024@" LLEN protocol_parse_queue_local
```

### 3. 查看 Celery 状态

```bash
# 查看 Worker 状态
celery -A celery_task inspect active

# 查看队列状态
celery -A celery_task inspect reserved

# 查看统计信息
celery -A celery_task inspect stats
```

## 常用开发流程

### 完整启动流程

```bash
# 1. 设置环境
./scripts/start_local_dev.sh

# 2. 启动 Celery Worker（新终端）
./scripts/start_celery.sh

# 3. 启动 FastAPI 应用（新终端）  
./scripts/start_app.sh

# 4. 访问应用
open http://localhost:8000/docs
```

### 测试流程

```bash
# 发送测试请求
curl -X POST "http://localhost:8000/protocols/extract-by-docx-async" \
  -H "Content-Type: application/json" \
  -d '{
    "protocol_file": {
      "file_name": "test.docx",
      "file_key": "test/sample.docx"
    }
  }'
```

### 关闭流程

```bash
# 1. 关闭 FastAPI（在应用终端按 Ctrl+C）

# 2. 关闭 Celery Worker
./scripts/stop_celery.sh

# 3. 退出虚拟环境
deactivate
```

## 故障排除

### 常见问题

1. **Redis 连接失败或频繁断开**
   ```bash
   # 运行连接诊断工具
   python scripts/diagnose_redis_connection.py

   # 手动检查 Redis 连接
   redis-cli -h *********** -p 6379 -a "wtg2024@" ping

   # 检查网络连通性
   ping ***********
   ```

2. **Celery 连接警告 (BrokenPipeError)**
   这是正常现象，Celery 会自动重连。如果频繁出现：
   ```bash
   # 使用本地开发专用配置（已自动启用）
   export APP_ENV=local

   # 检查网络稳定性
   python scripts/diagnose_redis_connection.py
   ```

3. **环境变量未生效**
   ```bash
   # 检查环境变量
   echo $APP_ENV $REDIS_DB $REDIS_TASK_DB

   # 重新设置环境
   ./scripts/start_local_dev.sh
   ```

4. **Celery Worker 无法启动**
   ```bash
   # 检查 Celery 配置
   python -c "from celery_task.celery import celery_app; print(celery_app.conf)"

   # 详细错误信息
   celery -A celery_task worker --loglevel=debug
   ```

5. **任务未执行**
   ```bash
   # 检查队列状态
   ./scripts/monitor_queues.sh

   # 检查 Worker 状态
   celery -A celery_task inspect active
   ```

### 日志查看

```bash
# Celery Worker 日志在终端直接显示

# 如果需要保存日志
./scripts/start_celery.sh 2>&1 | tee logs/celery.log

# FastAPI 日志
./scripts/start_app.sh 2>&1 | tee logs/app.log
```
