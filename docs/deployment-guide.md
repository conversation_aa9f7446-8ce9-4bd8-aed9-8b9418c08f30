# 环境隔离部署指南

## 问题解决方案总结

通过环境变量注入不同的 Redis DB 和队列名，实现 dev 和 main 环境的完全隔离。

### 核心改动

1. **配置文件修改**：
   - `config/settings.py`：添加环境变量支持和动态队列名生成
   - `celery_task/celery.py`：使用环境相关的应用名
   - `celery_task/celeryconfig.py`：使用环境相关的队列名
   - `circus.ini`：添加 APP_ENV 环境变量支持

2. **环境隔离机制**：
   - **应用名**：`protocol-parse-app-{APP_ENV}`
   - **队列名**：`protocol_parse_queue_{APP_ENV}`
   - **Redis DB**：dev=4, main=5, local=6

## 部署步骤

### 1. 更新代码

确保所有代码修改已经合并到对应分支。

### 2. 配置 K8s 环境变量

#### Dev 环境
```yaml
env:
- name: APP_ENV
  value: "dev"
- name: REDIS_DB
  value: "4"
- name: REDIS_TASK_DB
  value: "4"
```

#### Main 环境
```yaml
env:
- name: APP_ENV
  value: "main"
- name: REDIS_DB
  value: "5"
- name: REDIS_TASK_DB
  value: "5"
```

### 3. 部署顺序

建议按以下顺序部署：

1. **先部署 Dev 环境**
   - 验证配置正确
   - 测试任务隔离
   - 确认无问题后再部署 Main

2. **再部署 Main 环境**
   - 使用不同的 Redis DB
   - 验证与 Dev 环境完全隔离

### 4. 验证步骤

#### 4.1 配置验证
```bash
# 在容器内运行验证脚本
kubectl exec -it <pod-name> -- python scripts/verify_environment_isolation.py
```

#### 4.2 队列监控
```bash
# Dev 环境队列
redis-cli -h *********** -p 6379 -n 4 -a "wtg2024@" LLEN protocol_parse_queue_dev

# Main 环境队列  
redis-cli -h *********** -p 6379 -n 5 -a "wtg2024@" LLEN protocol_parse_queue_main
```

#### 4.3 实际测试
1. 向 Dev 环境发送请求
2. 检查只有 Dev 环境的队列有任务
3. 向 Main 环境发送请求
4. 检查只有 Main 环境的队列有任务

### 5. 监控和日志

#### 5.1 启动日志检查
应用启动时会打印：
```
加载 Celery 配置 - 环境: dev, 队列: protocol_parse_queue_dev
Celery 环境配置 - APP_ENV: dev, 队列: protocol_parse_queue_dev
```

#### 5.2 持续监控
使用提供的监控脚本：
```bash
watch -n 2 'echo "=== 队列状态 ===" && \
echo "DEV: $(redis-cli -h *********** -p 6379 -n 4 -a "wtg2024@" LLEN protocol_parse_queue_dev)" && \
echo "MAIN: $(redis-cli -h *********** -p 6379 -n 5 -a "wtg2024@" LLEN protocol_parse_queue_main)" && \
echo "完成"'
```

## 回滚方案

如果出现问题，可以快速回滚：

1. **移除环境变量**：删除 APP_ENV、REDIS_DB、REDIS_TASK_DB
2. **重启应用**：应用会使用默认配置（dev 环境）
3. **清理队列**：如果需要，清理新创建的队列

## 注意事项

1. **数据迁移**：现有的任务结果可能需要迁移或清理
2. **监控更新**：更新监控脚本以适配新的队列名
3. **文档更新**：更新相关的运维文档
4. **渐进部署**：建议先在 Dev 环境验证，再推广到 Main

## 常见问题

### Q: 如果忘记设置环境变量会怎样？
A: 应用会使用默认的 dev 环境配置，不会影响正常运行。

### Q: 可以动态切换环境吗？
A: 需要重启应用才能生效，因为配置在启动时加载。

### Q: 如何确认隔离生效？
A: 使用提供的验证脚本和监控命令，检查不同环境的队列状态。

### Q: 如果 Redis 连接失败怎么办？
A: 检查 Redis 配置和网络连接，确保指定的 DB 可以正常访问。

## 联系方式

如有问题，请联系开发团队或查看相关文档。
