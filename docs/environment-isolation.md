# 环境隔离配置说明

## 问题背景

之前 dev 和 main 环境共享同一个 Redis 实例和队列，导致：
- dev 环境的请求可能被 main 环境的 Celery Worker 处理
- main 环境的请求可能被 dev 环境的 Celery Worker 处理
- 调试时需要同时查看两个环境的日志

## 解决方案

通过环境变量实现 Redis DB 和队列名的隔离：

### 1. 环境变量配置

在 K8s 部署时注入以下环境变量：

#### Dev 环境
```yaml
env:
  - name: CELERY_ENV
    value: "dev"
  - name: REDIS_DB
    value: "4"
  - name: REDIS_TASK_DB
    value: "4"
```

#### Main 环境  
```yaml
env:
  - name: CELERY_ENV
    value: "main"
  - name: REDIS_DB
    value: "5"
  - name: REDIS_TASK_DB
    value: "5"
```

#### 本地开发环境
```bash
export CELERY_ENV=local
export REDIS_DB=6
export REDIS_TASK_DB=6
```

### 2. 自动生成的资源名称

根据 `CELERY_ENV` 环境变量，系统会自动生成：

- **Celery 应用名**: `protocol-parse-app-{CELERY_ENV}`
  - dev: `protocol-parse-app-dev`
  - main: `protocol-parse-app-main`
  - local: `protocol-parse-app-local`

- **队列名**: `protocol_parse_queue_{CELERY_ENV}`
  - dev: `protocol_parse_queue_dev`
  - main: `protocol_parse_queue_main`
  - local: `protocol_parse_queue_local`

### 3. Redis DB 分配

| 环境 | REDIS_DB | REDIS_TASK_DB | 说明 |
|------|----------|---------------|------|
| dev | 4 | 4 | 开发测试环境 |
| main | 5 | 5 | 生产环境 |
| local | 6 | 6 | 本地开发环境 |

### 4. 兼容性处理

- **默认行为**: 如果不设置 `CELERY_ENV`，默认为 `dev` 环境
- **向后兼容**: 保持原有的 Redis 连接配置不变
- **渐进式部署**: 可以先在一个环境启用，验证无问题后再推广

## 部署步骤

### 1. 更新 K8s 配置

在 dev 环境的 deployment.yaml 中添加：
```yaml
env:
  - name: CELERY_ENV
    value: "dev"
  - name: REDIS_DB
    value: "4"
  - name: REDIS_TASK_DB  
    value: "4"
```

在 main 环境的 deployment.yaml 中添加：
```yaml
env:
  - name: CELERY_ENV
    value: "main"
  - name: REDIS_DB
    value: "5"
  - name: REDIS_TASK_DB
    value: "5"
```

### 2. 验证隔离效果

部署后可以通过以下方式验证：

1. **查看日志**: 启动时会打印环境和队列信息
2. **Redis 监控**: 检查不同 DB 的队列情况
3. **任务测试**: 分别向两个环境发送请求，确认任务不会跨环境执行

## 监控和调试

### 1. 日志标识

启动时会打印：
```
加载 Celery 配置 - 环境: dev, 队列: protocol_parse_queue_dev
Celery 环境配置 - CELERY_ENV: dev, 队列: protocol_parse_queue_dev
```

### 2. Redis 监控

可以通过 Redis CLI 监控不同 DB 的队列：
```bash
# 监控 dev 环境 (DB 4)
redis-cli -h 47.98.45.40 -p 6379 -n 4 -a "wtg2024@" LLEN protocol_parse_queue_dev

# 监控 main 环境 (DB 5)  
redis-cli -h 47.98.45.40 -p 6379 -n 5 -a "wtg2024@" LLEN protocol_parse_queue_main
```

## 注意事项

1. **数据迁移**: 现有任务结果可能需要迁移或清理
2. **监控调整**: 需要更新监控脚本以适配新的队列名
3. **文档更新**: 更新相关的运维文档和监控配置
