# K8s 环境隔离配置示例
# 
# 使用方法：
# 1. 复制对应环境的配置到你的 deployment.yaml
# 2. 根据实际情况调整其他配置项
# 3. 部署并验证隔离效果

---
# Dev 环境配置示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yiya-ai-bot-dev
  namespace: dev
spec:
  template:
    spec:
      containers:
      - name: yiya-ai-bot
        image: your-registry/yiya-ai-bot:latest
        env:
        # 环境隔离关键配置
        - name: APP_ENV
          value: "dev"
        - name: REDIS_DB
          value: "4"
        - name: REDIS_TASK_DB
          value: "4"
        
        # 其他 Redis 配置（如果需要覆盖默认值）
        - name: REDIS_HOST
          value: "***********"
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        
        # 其他环境变量...
        - name: NACOS_SERVER
          value: "your-nacos-server"
        # ... 其他配置

---
# Main 环境配置示例  
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yiya-ai-bot-main
  namespace: main
spec:
  template:
    spec:
      containers:
      - name: yiya-ai-bot
        image: your-registry/yiya-ai-bot:latest
        env:
        # 环境隔离关键配置
        - name: APP_ENV
          value: "main"
        - name: REDIS_DB
          value: "5"
        - name: REDIS_TASK_DB
          value: "5"
        
        # 其他 Redis 配置（如果需要覆盖默认值）
        - name: REDIS_HOST
          value: "***********"
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        
        # 其他环境变量...
        - name: NACOS_SERVER
          value: "your-nacos-server"
        # ... 其他配置

---
# ConfigMap 示例 - 可选，用于集中管理环境配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: yiya-ai-bot-env-config
data:
  # Dev 环境配置
  dev.env: |
    APP_ENV=dev
    REDIS_DB=4
    REDIS_TASK_DB=4
    
  # Main 环境配置
  main.env: |
    APP_ENV=main
    REDIS_DB=5
    REDIS_TASK_DB=5

---
# Secret 示例 - Redis 密码等敏感信息
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
type: Opaque
data:
  # base64 编码的密码
  password: d3RnMjAyNEA=  # wtg2024@

---
# 监控用的 Service Monitor 示例（如果使用 Prometheus）
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: yiya-ai-bot-celery-monitor
spec:
  selector:
    matchLabels:
      app: yiya-ai-bot
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
