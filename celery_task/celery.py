import os

from celery import Celery
from celery.signals import celeryd_init

from logger.logger import app_logger
from config.settings import settings

# 创建 Celery 应用实例 - 根据环境动态命名
# 包含协议解析任务
celery_app = Celery(settings.CELERY_APP_NAME, include=["celery_task.protocol_task"])

# 根据环境选择配置文件
if settings.APP_ENV == "local":
    # 本地开发使用专用配置
    celery_app.config_from_object("celery_task.celeryconfig_local")
    config_type = "本地开发专用"
else:
    # 其他环境使用通用配置
    celery_app.config_from_object("celery_task.celeryconfig")
    config_type = "通用"

# 添加导入保护，避免在 uvicorn reload 模式下重复打印日志
if not hasattr(celery_app, "_config_loaded"):
    app_logger.info(f"加载 Celery {config_type}配置 - 环境: {settings.APP_ENV}, 队列: {settings.CELERY_QUEUE_NAME}")
    celery_app._config_loaded = True


# 信号处理器：在 Worker 启动后异步加载 Nacos 配置
@celeryd_init.connect
def init_worker(**kwargs):
    import threading
    import time

    # 检查是否应该在 Celery Worker 中启用 Nacos
    disable_nacos = os.environ.get("DISABLE_NACOS", "false").lower() == "true"
    enable_in_celery = os.environ.get("NACOS_ENABLE_IN_CELERY", "true").lower() == "true"

    if disable_nacos or not enable_in_celery:
        app_logger.info("Nacos 在 Celery Worker 中已被禁用，跳过加载")
        return

    app_logger.info("Celery Worker 启动完成，开始初始化...")

    # 1. 任务恢复功能（已禁用，依赖前端重新生成）
    def run_task_recovery():
        """运行任务恢复（已禁用）"""
        # 禁用自动任务恢复，避免重复提交测试任务
        # 依赖前端重新生成机制处理失败的任务
        app_logger.info("⚠️ 自动任务恢复已禁用（依赖前端重新生成机制）")
        return {"total_found": 0, "recovered": 0, "failed": 0, "skipped": 0}

    # 2. 然后异步加载 Nacos 配置
    def load_nacos_async():
        """异步加载 Nacos 配置，不阻塞 Worker 启动"""
        # 从环境变量获取重试配置
        max_retries = int(os.environ.get("NACOS_MAX_RETRIES", "3"))
        retry_delay = int(os.environ.get("NACOS_RETRY_DELAY", "5"))  # 增加延迟到5秒
        initial_delay = int(os.environ.get("NACOS_INITIAL_DELAY", "3"))  # 初始延迟3秒

        # 初始延迟，让 Worker 先完全启动
        time.sleep(initial_delay)

        for attempt in range(max_retries):
            try:
                from configurer.yy_nacos import init_nacos_manager

                app_logger.info(f"异步加载 Nacos 配置 (尝试 {attempt + 1}/{max_retries})")
                init_nacos_manager().init_client()
                app_logger.info("✅ Nacos 配置异步加载成功")
                return  # 成功加载，退出重试循环

            except Exception as e:
                app_logger.error(f"Nacos 配置加载失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                if attempt < max_retries - 1:
                    app_logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    app_logger.warning("⚠️ Nacos 配置最终加载失败，Worker 将在没有 Nacos 配置的情况下运行")
                    app_logger.warning("⚠️ 某些依赖 Nacos 配置的功能可能不可用，但 Worker 可以正常处理任务")

    # 禁用自动任务恢复 - 前端有重新生成逻辑，避免重复提交
    # run_task_recovery()
    app_logger.info("⚠️ 自动任务恢复已禁用，依赖前端重新生成机制")

    # 然后启动异步线程加载 Nacos，不阻塞 Worker 启动
    # nacos_thread = threading.Thread(target=load_nacos_async, daemon=True, name="NacosLoader")
    # nacos_thread.start()
    # app_logger.info("✅ Celery Worker 启动完成，Nacos 配置正在后台异步加载...")
