from urllib.parse import quote_plus

from config.settings import settings

broker_protocol = "redis"
broker_host = settings.REDIS_HOST
broker_port = settings.REDIS_PORT
broker_db = settings.REDIS_DB
broker_pwd = settings.REDIS_PASSWORD

result_db = settings.REDIS_TASK_DB

# 构建Redis URL（对密码进行URL编码以处理特殊字符如@）
# 如果密码为空，则不包含密码部分
if broker_pwd:
    encoded_pwd = quote_plus(broker_pwd)
    broker_url = "{}://:{}@{}:{}/{}".format(broker_protocol, encoded_pwd, broker_host, broker_port, broker_db)
    result_backend = "{}://:{}@{}:{}/{}".format(broker_protocol, encoded_pwd, broker_host, broker_port, result_db)

    # 配置验证：检查 URL 格式
    if "@@" in broker_url:
        raise ValueError(f"Redis URL 格式错误，包含双重@符号: {broker_url}")
else:
    broker_url = "{}://{}:{}/{}".format(broker_protocol, broker_host, broker_port, broker_db)
    result_backend = "{}://{}:{}/{}".format(broker_protocol, broker_host, broker_port, result_db)

# 记录配置信息（隐藏密码）
import logging

logger = logging.getLogger(__name__)
safe_url = broker_url.replace(encoded_pwd if broker_pwd else "", "***") if broker_pwd else broker_url
logger.info(f"Celery Broker URL: {safe_url}")
logger.info(f"Celery 环境配置 - CELERY_ENV: {settings.CELERY_ENV}, 队列: {settings.CELERY_QUEUE_NAME}")

# 任务序列化格式
task_serializer = "json"
# 结果序列化格式
result_serializer = "json"
# 接受的内容类型
accept_content = ["json"]
# 时区
timezone = "Asia/Shanghai"
# 启用UTC
enable_utc = True

# 任务结果失效时间
result_expire = settings.TASK_RESULT_EXPIRE_TIME

# 优化连接配置，支持多容器环境
broker_transport_options = {
    "socket_connect_timeout": 10,  # 缩短连接超时
    "socket_timeout": 30.0,  # 缩短读取超时
    "retry_on_timeout": True,
    # 优化连接池配置，支持多Pod并发
    "connection_pool_kwargs": {
        "max_connections": 50,  # 增加最大连接数以支持多Pod
        "retry_on_timeout": True,
        "socket_connect_timeout": 10,
        "socket_timeout": 30,
        # 添加连接池健康检查
        "health_check_interval": 30,
    },
    # 优化重试策略
    "retry_policy": {
        "timeout": 60.0,  # 缩短重试总超时时间
        "interval_start": 0.1,
        "interval_step": 0.2,
        "interval_max": 2.0,  # 缩短最大重试间隔
        "max_retries": 5,  # 减少最大重试次数
    },
}

# 连接池配置
broker_connection_retry_on_startup = True
broker_connection_retry = True
broker_connection_max_retries = 10

# 任务确认配置
task_acks_late = True
worker_prefetch_multiplier = 1

# 连接丢失时的任务处理（解决警告信息）
worker_cancel_long_running_tasks_on_connection_loss = True

# 问题2修复：添加Worker级别的超时配置
# 任务超时设置（秒）
task_time_limit = 300  # 硬超时：5分钟
task_soft_time_limit = 240  # 软超时：4分钟

# Worker超时设置
worker_disable_rate_limits = False
worker_max_tasks_per_child = 20  # 每个Worker进程最多处理20个任务后重启，防止内存泄漏
worker_max_memory_per_child = 4096000  # 每个Worker进程最大内存4GB

# 结果后端连接配置
result_backend_transport_options = broker_transport_options

# 使用环境相关的队列名，实现环境隔离
task_default_queue = settings.CELERY_QUEUE_NAME
task_routes = {
    "extract_protocol_task": {"queue": settings.CELERY_QUEUE_NAME},
}

# 多容器环境优化配置
import os

POD_NAME = os.environ.get("HOSTNAME", "unknown-pod")
WORKER_ID = f"{POD_NAME}-{os.getpid()}"

# 添加Worker标识，便于监控和调试
worker_log_format = f"[{WORKER_ID}] %(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 任务执行监控配置
task_track_started = True  # 跟踪任务开始状态
task_send_sent_event = True  # 发送任务发送事件

# 多容器负载均衡优化
worker_prefetch_multiplier = 1  # 确保任务均匀分发
task_acks_late = True  # 任务完成后才确认，防止任务丢失
