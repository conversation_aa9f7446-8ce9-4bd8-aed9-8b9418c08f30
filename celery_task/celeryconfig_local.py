# 本地开发环境专用 Celery 配置
# 针对本地网络环境和开发需求优化

from urllib.parse import quote_plus
from config.settings import settings

broker_protocol = "redis"
broker_host = settings.REDIS_HOST
broker_port = settings.REDIS_PORT
broker_db = settings.REDIS_DB
broker_pwd = settings.REDIS_PASSWORD

result_db = settings.REDIS_TASK_DB

# 构建Redis URL（对密码进行URL编码以处理特殊字符如@）
if broker_pwd:
    encoded_pwd = quote_plus(broker_pwd)
    broker_url = "{}://:{}@{}:{}/{}".format(broker_protocol, encoded_pwd, broker_host, broker_port, broker_db)
    result_backend = "{}://:{}@{}:{}/{}".format(broker_protocol, encoded_pwd, broker_host, broker_port, result_db)
else:
    broker_url = "{}://{}:{}/{}".format(broker_protocol, broker_host, broker_port, broker_db)
    result_backend = "{}://{}:{}/{}".format(broker_protocol, broker_host, broker_port, result_db)

# 记录配置信息（隐藏密码）
import logging
logger = logging.getLogger(__name__)
safe_url = broker_url.replace(encoded_pwd if broker_pwd else "", "***") if broker_pwd else broker_url
logger.info(f"本地开发 Celery Broker URL: {safe_url}")
logger.info(f"本地开发 Celery 环境配置 - CELERY_ENV: {settings.CELERY_ENV}, 队列: {settings.CELERY_QUEUE_NAME}")

# 任务序列化格式
task_serializer = "json"
result_serializer = "json"
accept_content = ["json"]
timezone = "Asia/Shanghai"
enable_utc = True

# 任务结果失效时间
result_expire = settings.TASK_RESULT_EXPIRE_TIME

# 本地开发优化的连接配置
broker_transport_options = {
    # 本地开发网络配置
    "socket_connect_timeout": 20,  # 本地网络可能较慢
    "socket_timeout": 120.0,       # 增加超时时间
    "retry_on_timeout": True,
    "socket_keepalive": True,      # 保持连接活跃
    "socket_keepalive_options": {
        "TCP_KEEPIDLE": 120,       # 2分钟后开始keepalive
        "TCP_KEEPINTVL": 60,       # 每分钟探测一次
        "TCP_KEEPCNT": 3,          # 3次失败后断开
    },
    
    # 本地开发连接池配置
    "connection_pool_kwargs": {
        "max_connections": 5,      # 本地开发只需要少量连接
        "retry_on_timeout": True,
        "socket_connect_timeout": 20,
        "socket_timeout": 120,
        "socket_keepalive": True,
        "socket_keepalive_options": {
            "TCP_KEEPIDLE": 120,
            "TCP_KEEPINTVL": 60,
            "TCP_KEEPCNT": 3,
        },
        "health_check_interval": 120,  # 2分钟健康检查
    },
    
    # 本地开发重试策略
    "retry_policy": {
        "timeout": 180.0,          # 3分钟总超时
        "interval_start": 1.0,     # 1秒开始重试
        "interval_step": 1.0,      # 每次增加1秒
        "interval_max": 10.0,      # 最大10秒间隔
        "max_retries": 15,         # 最多15次重试
    },
}

# 本地开发连接配置
broker_connection_retry_on_startup = True
broker_connection_retry = True
broker_connection_max_retries = 30     # 本地开发增加重试次数
broker_connection_retry_delay = 3.0    # 3秒重试延迟

# 本地开发心跳配置
broker_heartbeat = 120                 # 2分钟心跳
broker_heartbeat_checkrate = 1.0       # 心跳检查频率

# 任务确认配置
task_acks_late = True
worker_prefetch_multiplier = 1

# 连接丢失时的任务处理
worker_cancel_long_running_tasks_on_connection_loss = True

# 本地开发任务超时设置
task_time_limit = 600      # 10分钟硬超时（本地调试可能需要更长时间）
task_soft_time_limit = 480 # 8分钟软超时

# 本地开发 Worker 配置
worker_disable_rate_limits = True     # 本地开发禁用速率限制
worker_max_tasks_per_child = 50       # 本地开发增加任务数
worker_max_memory_per_child = 2048000 # 2GB内存限制

# 结果后端连接配置
result_backend_transport_options = broker_transport_options

# 使用环境相关的队列名
task_default_queue = settings.CELERY_QUEUE_NAME
task_routes = {
    "extract_protocol_task": {"queue": settings.CELERY_QUEUE_NAME},
}

# 本地开发 Worker 标识
import os
POD_NAME = os.environ.get("HOSTNAME", "local-dev")
WORKER_ID = f"{POD_NAME}-{os.getpid()}"

# 本地开发日志格式
worker_log_format = f"[{WORKER_ID}] %(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 任务执行监控配置
task_track_started = True
task_send_sent_event = True

# 本地开发负载均衡
worker_prefetch_multiplier = 1
task_acks_late = True

# 本地开发调试配置
worker_log_color = True               # 启用彩色日志
worker_redirect_stdouts = False       # 不重定向标准输出，便于调试
worker_redirect_stdouts_level = "INFO"

# 本地开发事件监控
worker_send_task_events = True        # 发送任务事件
task_send_sent_event = True          # 发送任务发送事件

# 本地开发错误处理
task_reject_on_worker_lost = True     # Worker丢失时拒绝任务
task_ignore_result = False            # 不忽略结果

print(f"📋 本地开发 Celery 配置加载完成")
print(f"   环境: {settings.CELERY_ENV}")
print(f"   队列: {settings.CELERY_QUEUE_NAME}")
print(f"   Redis DB: {settings.REDIS_DB}")
print(f"   连接超时: {broker_transport_options['socket_connect_timeout']}s")
print(f"   心跳间隔: {broker_heartbeat}s")
