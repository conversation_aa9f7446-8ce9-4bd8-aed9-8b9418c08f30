#!/usr/bin/env python
import time

from fastapi import APIRouter

from models.result import make_fail, make_success
from models.tense_vo import TenseConvertRequest, TenseConvertResponse, TenseSentenceResult
from utils.tense_utils import convert_future_to_past
from logger.logger import app_logger


router = APIRouter(prefix="/text", tags=["text"], include_in_schema=False)


@router.post("/tense/convert", response_model=TenseConvertResponse)
async def convert_tense(req: TenseConvertRequest):
    start = time.time()
    try:
        # 记录用户输入
        app_logger.info(f"收到时态转换请求，输入文本长度: {len(req.text) if req.text else 0}")
        app_logger.debug(f"输入文本内容: {req.text[:200]}{'...' if len(req.text) > 200 else ''}")

        if not req.text or not req.text.strip():
            app_logger.warning("时态转换请求失败：文本为空")
            return make_fail(400, "text is required")

        # 执行时态转换
        results = convert_future_to_past(req.text)
        
        # 记录转换结果统计
        # total_sentences = len(results)
        # future_tense_sentences = sum(1 for item in results if item.get('is_future_tense', False))
        # app_logger.info(f"时态转换完成，总句子数: {total_sentences}, 检测到将来时句子数: {future_tense_sentences}")
        
        # 记录转换后的文本示例
        if results:
            sample_result = results
            app_logger.debug(f"转换示例 - 原句: {sample_result.get('original_text', '')[:100]}...")
            app_logger.debug(f"转换示例 - 新句: {sample_result.get('target_text', '')[:100]}...")

        # 构建响应
        item = TenseSentenceResult(**results)
        duration = int((time.time() - start) * 1000)
        
        app_logger.info(f"时态转换接口响应成功，耗时: {duration}ms")
        return make_success(item, duration)
        
    except Exception as e:
        app_logger.error(f"时态转换接口异常: {str(e)}", exc_info=True)
        return make_fail(500, f"Internal Server Error: {e}")
