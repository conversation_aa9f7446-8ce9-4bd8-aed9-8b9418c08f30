#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CIOMS报告生成API
"""

import json
import os
import tempfile
import time
import uuid
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException

from logger.logger import app_logger
from models.cioms_request import CiomsGenerateRequest, CiomsGenerateResponse
from models.result import make_fail, make_success
from utils.cioms_utils import CiomsDocxFiller
from utils.oss_utils import get_file_url, write_file
router = APIRouter(include_in_schema=False)


def get_template_path(company_name: str) -> Optional[str]:
    """
    根据公司名称获取对应的模板文件路径
    
    Args:
        company_name: 公司名称
        
    Returns:
        模板文件路径，如果找不到则返回None
    """
    # 获取当前文件所在目录
    current_dir = os.path.dirname(__file__)
    # 构建模板目录路径
    template_dir = os.path.join(current_dir, "..", "template", "cioms")
    
    # 根据公司名称构建模板文件名
    template_filename = f"{company_name.lower()}.docx"
    template_path = os.path.join(template_dir, template_filename)
    
    # 检查文件是否存在
    if os.path.exists(template_path):
        app_logger.info(f"找到模板文件: {template_path}")
        return template_path
    else:
        app_logger.warning(f"未找到模板文件: {template_path}")
        return None


def generate_output_filename(company_name: str) -> str:
    """
    生成输出文件名
    
    Args:
        company_name: 公司名称
        
    Returns:
        生成的文件名
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]  # 取UUID的前8位
    return f"cioms_report_{company_name}_{timestamp}_{unique_id}.docx"


def generate_oss_key(filename: str) -> str:
    """
    生成OSS存储键
    
    Args:
        filename: 文件名
        
    Returns:
        OSS存储键
    """
    date_prefix = datetime.now().strftime("%Y/%m/%d")
    return f"cioms/{date_prefix}/{filename}"


@router.post("/cioms/generate")
async def generate_cioms_report(request: CiomsGenerateRequest):
    """
    生成CIOMS报告
    
    Args:
        request: CIOMS报告生成请求
        
    Returns:
        生成的报告文件信息
    """
    start_time = time.time()
    temp_output_path = None
    
    try:
        app_logger.info(f"开始生成CIOMS报告，公司: {request.company_name}")
        
        # 1. 验证和解析JSON数据
        try:
            payload_data = json.loads(request.json_data)
            app_logger.info("JSON数据解析成功")
        except json.JSONDecodeError as e:
            app_logger.error(f"JSON数据解析失败: {e}")
            raise HTTPException(status_code=400, detail=f"无效的JSON数据: {e}")
        
        # 2. 获取模板文件路径
        template_path = get_template_path(request.company_name)
        if not template_path:
            error_msg = f"未找到公司 '{request.company_name}' 对应的模板文件"
            app_logger.error(error_msg)
            raise HTTPException(status_code=404, detail=error_msg)
        
        # 3. 生成输出文件名和路径
        output_filename = generate_output_filename(request.company_name)
        
        # 创建临时文件用于输出
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_output_path = temp_file.name
        
        app_logger.info(f"使用模板: {template_path}")
        app_logger.info(f"输出文件: {output_filename}")
        
        # 4. 使用CiomsDocxFiller填充模板
        filler_start = time.time()
        try:
            filler = CiomsDocxFiller(enable_debug=True)
            filler.fill_docx(template_path, payload_data, temp_output_path)
            filler_time = time.time() - filler_start
            app_logger.info(f"CIOMS模板填充完成，耗时: {filler_time:.2f}秒")
        except Exception as e:
            app_logger.error(f"CIOMS模板填充失败: {e}")
            raise HTTPException(status_code=500, detail=f"模板填充失败: {e}")
        
        # 5. 上传到OSS
        upload_start = time.time()
        try:
            oss_key = generate_oss_key(output_filename)
            write_file(temp_output_path, oss_key)
            
            # 获取文件访问URL
            file_url = get_file_url(oss_key)
            upload_time = time.time() - upload_start
            app_logger.info(f"文件上传到OSS成功，耗时: {upload_time:.2f}秒")
            app_logger.info(f"OSS键: {oss_key}")
            app_logger.info(f"访问URL: {file_url}")
        except Exception as e:
            app_logger.error(f"文件上传到OSS失败: {e}")
            raise HTTPException(status_code=500, detail=f"文件上传失败: {e}")
        
        # 6. 构建响应
        response = CiomsGenerateResponse(
            file_url=file_url,
            file_key=oss_key,
            file_name=output_filename
        )
        
        total_time = time.time() - start_time
        app_logger.info(f"CIOMS报告生成完成，总耗时: {total_time:.2f}秒")
        
        return make_success(response, int(total_time))
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        total_time = time.time() - start_time
        app_logger.error(f"CIOMS报告生成失败，耗时: {total_time:.2f}秒，错误: {e}")
        return make_fail(500, f"生成报告时发生错误: {e}")
    
    finally:
        # 清理临时文件
        if temp_output_path and os.path.exists(temp_output_path):
            try:
                os.unlink(temp_output_path)
                app_logger.info("临时文件清理完成")
            except Exception as cleanup_error:
                app_logger.warning(f"临时文件清理失败: {cleanup_error}")


@router.get("/cioms/templates")
async def list_available_templates():
    """
    列出可用的CIOMS模板
    
    Returns:
        可用模板列表
    """
    try:
        current_dir = os.path.dirname(__file__)
        template_dir = os.path.join(current_dir, "..", "template", "cioms")
        
        templates = []
        if os.path.exists(template_dir):
            for filename in os.listdir(template_dir):
                if filename.endswith('.docx'):
                    company_name = os.path.splitext(filename)[0]
                    templates.append({
                        "company_name": company_name,
                        "template_file": filename,
                        "template_path": os.path.join(template_dir, filename)
                    })
        
        app_logger.info(f"找到 {len(templates)} 个可用模板")
        return make_success(templates, 0)
        
    except Exception as e:
        app_logger.error(f"获取模板列表失败: {e}")
        return make_fail(500, f"获取模板列表失败: {e}")
