import os

from pydantic import field_validator
from pydantic_settings import BaseSettings

# pydantic-settings 中的 BaseSettings 类会自动从系统/k8s环境变量中读取配置
class Settings(BaseSettings):
    # Redis 连接配置 - 支持环境变量覆盖
    REDIS_HOST: str = "***********"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = "wtg2024@"  # TODO: 移除硬编码，使用环境变量

    # Redis DB 配置 - 通过环境变量区分环境
    # 默认值：dev=4, main=5, 本地开发=6
    REDIS_DB: int = 4  # 默认 dev 环境使用 db4
    REDIS_TASK_DB: int = 4  # 默认 dev 环境结果存储也用 db4

    # 环境标识 - 用于区分队列名和日志
    CELERY_ENV: str = "dev"  # 默认开发环境，可通过 CELERY_ENV 环境变量覆盖

    # 兼容性配置（保留原有配置）
    REDIS_DB_LOCAL: int = 6  # 本地环境使用 db6，避免与 dev/main 冲突
    REDIS_TASK_DB_LOCAL: int = 6

    # 任务配置
    TASK_RESULT_EXPIRE_TIME: int = 60 * 60 * 24  # 24小时
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT: int = 3600
    MAX_RETRY_TIMES: int = 3

    # 队列配置 - 根据环境动态生成队列名
    @property
    def CELERY_QUEUE_NAME(self) -> str:
        """根据环境生成唯一的队列名"""
        return f"protocol_parse_queue_{self.CELERY_ENV}"

    @property
    def CELERY_APP_NAME(self) -> str:
        """根据环境生成唯一的应用名"""
        return f"protocol-parse-app-{self.CELERY_ENV}"

    @field_validator("REDIS_PORT", mode="before")
    @classmethod
    def parse_redis_port(cls, v):
        """处理 REDIS_PORT 空值"""
        if v == "" or v is None:
            return 6379  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 6379
        return v

    @field_validator("REDIS_DB", mode="before")
    @classmethod
    def parse_redis_db(cls, v):
        """处理 REDIS_DB 空值"""
        if v == "" or v is None:
            return 4  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 4
        return v

    @field_validator("REDIS_TASK_DB", mode="before")
    @classmethod
    def parse_redis_task_db(cls, v):
        """处理 REDIS_TASK_DB 空值"""
        if v == "" or v is None:
            return 4  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 4
        return v

    @field_validator("TASK_RESULT_EXPIRE_TIME", mode="before")
    @classmethod
    def parse_task_result_expire_time(cls, v):
        """处理 TASK_RESULT_EXPIRE_TIME 空值"""
        if v == "" or v is None:
            return 60 * 60 * 24  # 默认值：24小时
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 60 * 60 * 24
        return v

    @field_validator("MAX_CONCURRENT_TASKS", mode="before")
    @classmethod
    def parse_max_concurrent_tasks(cls, v):
        """处理 MAX_CONCURRENT_TASKS 空值"""
        if v == "" or v is None:
            return 10  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 10
        return v

    @field_validator("TASK_TIMEOUT", mode="before")
    @classmethod
    def parse_task_timeout(cls, v):
        """处理 TASK_TIMEOUT 空值"""
        if v == "" or v is None:
            return 3600  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 3600
        return v

    @field_validator("MAX_RETRY_TIMES", mode="before")
    @classmethod
    def parse_max_retry_times(cls, v):
        """处理 MAX_RETRY_TIMES 空值"""
        if v == "" or v is None:
            return 3  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 3
        return v

    @field_validator("REDIS_HOST", mode="before")
    @classmethod
    def parse_redis_host(cls, v):
        """处理 REDIS_HOST 空值"""
        if v == "" or v is None:
            return "***********"  # 默认值
        return v

    @field_validator("REDIS_PASSWORD", mode="before")
    @classmethod
    def parse_redis_password(cls, v):
        """处理 REDIS_PASSWORD 空值"""
        if v == "" or v is None:
            return "wtg2024@"  # 默认值
        return v

    @field_validator("CELERY_ENV", mode="before")
    @classmethod
    def parse_celery_env(cls, v):
        """处理 CELERY_ENV 空值和标准化"""
        if v == "" or v is None:
            return "dev"  # 默认开发环境
        # 标准化环境名称
        env_map = {
            "development": "dev",
            "production": "main",
            "prod": "main",
            "staging": "staging",
            "test": "test"
        }
        return env_map.get(v.lower(), v.lower())

    # 协议解析相关配置
    PROTOCOL_PARSE_FOLDER: str = "protocol_parse_files"
    if not os.path.exists(PROTOCOL_PARSE_FOLDER):
        os.makedirs(PROTOCOL_PARSE_FOLDER)


settings = Settings()
