# 环境变量重命名：APP_ENV → CELERY_ENV

## 修改说明

根据领导要求，将环境变量名从 `APP_ENV` 改为 `CELERY_ENV`，以更准确地反映其用途。

## 修改范围

### 📁 核心配置文件

1. **`config/settings.py`**
   - `APP_ENV: str = "dev"` → `CELERY_ENV: str = "dev"`
   - 更新所有引用：`self.APP_ENV` → `self.CELERY_ENV`
   - 更新验证器：`parse_app_env` → `parse_celery_env`

2. **`celery_task/celery.py`**
   - 更新环境判断：`settings.APP_ENV` → `settings.CELERY_ENV`
   - 更新日志输出中的环境变量名

3. **`celery_task/celeryconfig.py`**
   - 更新日志输出：`APP_ENV` → `CELERY_ENV`

4. **`celery_task/celeryconfig_local.py`**
   - 更新本地开发配置中的环境变量引用

### 🐳 部署配置文件

5. **`APP-META/docker-config/environment/common/app/conf/circus.ini`**
   - 环境变量定义：`APP_ENV=$APP_ENV` → `CELERY_ENV=$CELERY_ENV`

6. **`k8s-examples/environment-isolation.yaml`**
   - K8s 环境变量配置：`APP_ENV` → `CELERY_ENV`
   - ConfigMap 配置更新

### 🔧 脚本文件

7. **`scripts/start_local_dev.sh`**
   - 环境变量设置：`export APP_ENV=local` → `export CELERY_ENV=local`
   - 输出显示更新

8. **`scripts/start_celery.sh`**
   - 环境变量引用：`$APP_ENV` → `$CELERY_ENV`
   - 队列名显示更新

9. **`scripts/start_app.sh`**
   - 环境变量设置和显示更新

10. **`scripts/verify_environment_isolation.py`**
    - 所有 `settings.APP_ENV` → `settings.CELERY_ENV`
    - 环境变量列表更新

11. **`scripts/test_environment_isolation.py`**
    - 环境判断和显示更新

12. **`scripts/diagnose_redis_connection.py`**
    - 诊断输出中的环境变量名更新

### 📚 文档文件

13. **所有 `.md` 文档文件**
    - 批量替换：`APP_ENV` → `CELERY_ENV`
    - 包括：
      - `docs/environment-isolation.md`
      - `docs/deployment-guide.md`
      - `docs/local-development-guide.md`
      - `docs/celery-connection-issues.md`
      - `QUICK_START.md`
      - `ENVIRONMENT_ISOLATION_README.md`

14. **`.env.example`**
    - 配置模板更新：`APP_ENV=local` → `CELERY_ENV=local`

## 环境变量映射

| 环境 | 旧变量名 | 新变量名 | Redis DB | 队列名 |
|------|----------|----------|----------|--------|
| 本地开发 | `APP_ENV=local` | `CELERY_ENV=local` | 6 | `protocol_parse_queue_local` |
| 开发环境 | `APP_ENV=dev` | `CELERY_ENV=dev` | 4 | `protocol_parse_queue_dev` |
| 生产环境 | `APP_ENV=main` | `CELERY_ENV=main` | 5 | `protocol_parse_queue_main` |

## 部署配置示例

### Dev 环境
```yaml
env:
- name: CELERY_ENV
  value: "dev"
- name: REDIS_DB
  value: "4"
- name: REDIS_TASK_DB
  value: "4"
```

### Main/Production 环境
```yaml
env:
- name: CELERY_ENV
  value: "main"  # 或 "production"
- name: REDIS_DB
  value: "5"
- name: REDIS_TASK_DB
  value: "5"
```

## 验证方法

### 1. 本地验证
```bash
# 启动本地开发环境
./scripts/start_local_dev.sh

# 应该看到：
# ✅ 环境变量已设置：
#    CELERY_ENV: local
#    Redis DB: 6
```

### 2. 配置验证
```bash
# 运行验证脚本
python scripts/verify_environment_isolation.py

# 应该看到：
# 当前环境: local
# 队列名: protocol_parse_queue_local
```

### 3. 不同环境测试
```bash
# 测试 main 环境
export CELERY_ENV=main REDIS_DB=5 REDIS_TASK_DB=5
python scripts/verify_environment_isolation.py

# 应该看到：
# 当前环境: main
# 队列名: protocol_parse_queue_main
```

## 兼容性说明

### ✅ 向后兼容
- 如果不设置 `CELERY_ENV`，默认使用 `dev` 环境
- 现有的 Redis 连接配置保持不变
- 队列命名规则保持一致

### ⚠️ 注意事项
1. **K8s 部署时**：确保将 `APP_ENV` 改为 `CELERY_ENV`
2. **监控脚本**：更新相关的监控和告警配置
3. **文档同步**：确保团队了解新的环境变量名

## 迁移检查清单

- [x] 核心配置文件更新
- [x] Celery 配置文件更新  
- [x] 部署配置文件更新
- [x] 脚本文件更新
- [x] 文档文件更新
- [x] 示例配置更新
- [x] 本地验证通过
- [ ] Dev 环境部署验证
- [ ] Main 环境部署验证
- [ ] 监控配置更新
- [ ] 团队培训完成

## 总结

✅ **所有 `APP_ENV` 已成功替换为 `CELERY_ENV`**

- **影响范围**：15+ 个文件，涵盖配置、脚本、文档
- **功能保持**：环境隔离机制完全不变
- **命名更准确**：`CELERY_ENV` 更明确地表示 Celery 环境配置
- **验证通过**：本地环境测试正常

现在可以安全地部署到各个环境，使用新的 `CELERY_ENV` 环境变量！🎉
