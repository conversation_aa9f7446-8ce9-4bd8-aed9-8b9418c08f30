# 🚀 本地开发快速启动指南

## 一键启动

```bash
# 1. 准备环境
./scripts/start_local_dev.sh

# 2. 启动 Celery Worker（新终端）
./scripts/start_celery.sh

# 3. 启动 FastAPI 应用（新终端）
./scripts/start_app.sh
```

## 常用命令

### 环境管理
```bash
# 准备本地开发环境
./scripts/start_local_dev.sh

# 验证环境配置
python scripts/verify_environment_isolation.py

# 测试环境隔离
python scripts/test_environment_isolation.py
```

### Celery 管理
```bash
# 启动 Celery Worker
./scripts/start_celery.sh

# 关闭 Celery Worker
./scripts/stop_celery.sh

# 重启 Celery Worker
./scripts/restart_celery.sh
```

### 应用管理
```bash
# 启动 FastAPI 应用
./scripts/start_app.sh

# 手动启动（如果脚本有问题）
source .venv/bin/activate
export CELERY_ENV=local REDIS_DB=6 REDIS_TASK_DB=6
uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### 监控和调试
```bash
# 检查队列状态
./scripts/monitor_queues.sh

# 实时监控队列
./scripts/monitor_queues.sh --watch

# 检查 Celery Worker 状态
celery -A celery_task inspect active

# 检查 Redis 连接
redis-cli -h *********** -p 6379 -n 6 -a "wtg2024@" ping
```

## 环境变量

### 核心配置
```bash
export CELERY_ENV=local          # 环境标识
export REDIS_DB=6             # Redis 数据库
export REDIS_TASK_DB=6        # 任务结果数据库
export DISABLE_NACOS=true     # 禁用 Nacos
```

### 使用 .env 文件
```bash
# 复制模板
cp .env.example .env

# 编辑配置
vim .env

# 加载环境变量
set -a && source .env && set +a
```

## 访问地址

- **API 文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

## 测试请求

```bash
# 发送测试请求
curl -X POST "http://localhost:8000/protocols/extract-by-docx-async" \
  -H "Content-Type: application/json" \
  -d '{
    "protocol_file": {
      "file_name": "test.docx",
      "file_key": "test/sample.docx"
    }
  }'
```

## 故障排除

### Redis 连接失败
```bash
# 检查 Redis 连接
redis-cli -h *********** -p 6379 -a "wtg2024@" ping

# 检查网络
ping ***********
```

### Celery Worker 无法启动
```bash
# 检查环境变量
echo $CELERY_ENV $REDIS_DB $REDIS_TASK_DB

# 检查 Python 路径
python -c "from celery_task.celery import celery_app; print('OK')"

# 查看详细错误
celery -A celery_task worker --loglevel=debug
```

### 端口被占用
```bash
# 查看占用进程
lsof -i :8000

# 杀死占用进程
lsof -ti :8000 | xargs kill -9
```

## 环境隔离验证

### 队列隔离
```bash
# 本地环境队列
redis-cli -h *********** -p 6379 -n 6 -a "wtg2024@" LLEN protocol_parse_queue_local

# Dev 环境队列
redis-cli -h *********** -p 6379 -n 4 -a "wtg2024@" LLEN protocol_parse_queue_dev

# Main 环境队列
redis-cli -h *********** -p 6379 -n 5 -a "wtg2024@" LLEN protocol_parse_queue_main
```

### 应用隔离
- **本地**: `protocol-parse-app-local` + `protocol_parse_queue_local` (DB 6)
- **Dev**: `protocol-parse-app-dev` + `protocol_parse_queue_dev` (DB 4)
- **Main**: `protocol-parse-app-main` + `protocol_parse_queue_main` (DB 5)

## 关闭流程

```bash
# 1. 关闭 FastAPI（在应用终端按 Ctrl+C）

# 2. 关闭 Celery Worker
./scripts/stop_celery.sh

# 3. 退出虚拟环境
deactivate
```

---

**🎉 现在你可以在本地安全地开发和测试，不会影响 dev 和 main 环境！**
