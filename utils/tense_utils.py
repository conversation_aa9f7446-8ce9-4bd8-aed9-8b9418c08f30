import spacy
import lemminflect  # noqa: F401  确保注册 Token._.inflect 扩展
from spacy.tokens import Token


class TenseConverter:
    def __init__(self):
        # 初始化spaCy模型
        self.nlp = spacy.load("en_core_web_sm")
        
    def _split_by_sentence(self, text, delimiters):
        """按句子分隔符切分文本"""
        segments = [text]
        for delimiter in delimiters:
            new_segments = []
            for segment in segments:
                parts = segment.split(delimiter)
                for i, part in enumerate(parts):
                    if i < len(parts) - 1:  # 不是最后一个部分
                        new_segments.append(part + delimiter)
                    else:  # 最后一个部分
                        if part:  # 如果不为空
                            new_segments.append(part)
            segments = new_segments
        return segments

    def _is_subject_plural_for_verb(self, verb_token: Token) -> bool:
        """
        为给定的动词token查找其主语并判断是否为复数。
        查找 nsubj (主动) 或 nsubjpass (被动)。
        简化并尝试使其更稳健。
        """
        # --- 策略：直接在 verb_token 的子节点中查找主语 ---
        for child in verb_token.children:
            if child.dep_ == "nsubj":
                return self._is_token_plural(child)
            elif child.dep_ == "nsubjpass":
                return self._is_token_plural(child)

        # --- 策略：如果动词在从句中 (relcl, acl)，主语可能是被修饰的名词 ---
        if verb_token.dep_ in ("relcl", "acl"):
            modified_noun = verb_token.head
            if modified_noun:
                return self._is_token_plural(modified_noun)

        # --- 策略：向上游走一层，查找主句动词的主语 (有限尝试) ---
        if verb_token.dep_ in ("xcomp", "ccomp") and verb_token.head != verb_token:  # Avoid ROOT
            ancestor_verb = verb_token.head
            for child in ancestor_verb.children:
                if child.dep_ == "nsubj":
                    return self._is_token_plural(child)
                elif child.dep_ == "nsubjpass":
                    return self._is_token_plural(child)

        # 如果以上方法都找不到，返回默认值
        return False  # 默认单数

    def _is_token_plural(self, token: Token) -> bool:
        """判断一个token（通常是名词或代词）是否为复数。"""
        # 代词
        if token.text.lower() in ('i', 'he', 'she', 'it') or token.lemma_.lower() in ('this', 'that'):
            return False
        if token.text.lower() in ('you', 'we', 'they') or token.lemma_.lower() in ('these', 'those'):
            return True
        # 名词
        if token.pos_ == "NOUN":
            # 使用 spaCy 的 morphological 信息 (最可靠)
            if hasattr(token, 'morph'):
                morph_dict = token.morph.to_dict()
                if morph_dict.get("Number") == "Plur":
                    return True
                elif morph_dict.get("Number") == "Sing":
                    return False
            # 启发式方法 - 检查限定词
            for child in token.children:
                if child.dep_ == "det" and child.text.lower() in ('these', 'those'):
                    return True
                if child.dep_ == "det" and child.text.lower() in ('this', 'that'):
                    return False
            return False  # 默认单数
        return False  # 默认单数

    def process_text(self, text: str) -> dict:
        """
        处理输入文本，执行时态检测和转换。
        整段输入，整段输出。
        """
        doc = self.nlp(text)
        original_text = text
        new_tokens = [token.text_with_ws for token in doc]
        tokens_modified = False
        original_chars = []
        target_chars = []
        is_future = False
        convert_infos = []  # 新增：用于存储每个修改短语的详细信息
        has_negation = False # Initialize has_negation
        not_token = None # Initialize not_token

        i = 0
        while i < len(doc):
            token = doc[i]

            # --- 检测并转换 "will/shall + ..." 结构 ---
            if token.lemma_ in ["will", "shall"] and token.pos_ == "AUX":
                is_future = True

                # 查找 will/shall 后面的第一个助动词或动词 (跳过副词等)
                j = i + 1
                # 记录will和be之间的副词
                adverbs_between = []
                while j < len(doc) and doc[j].pos_ not in ("VERB", "AUX"):
                    adverbs_between.append(doc[j])
                    j += 1

                if j < len(doc):
                    next_token = doc[j]

                    # --- 优化后的逻辑：处理 "will be ..." ---
                    if next_token.lemma_ == "be":
                        k = j + 1
                        while k < len(doc) and doc[k].pos_ not in ("VERB", "AUX"):
                            k += 1

                        if k < len(doc):
                            main_verb_token = doc[k]
                            # 情况 1.1: 被动语态 "will be VBN"
                            if main_verb_token.tag_ == "VBN":
                                is_plural = self._is_subject_plural_for_verb(main_verb_token)
                                # 记录原始短语的起止位置和文本
                                original_start = token.idx
                                original_end = main_verb_token.idx + len(main_verb_token.text)
                                original_phrase = text[original_start:original_end]
                                
                                # 检查是否有否定词
                                has_not = False
                                not_token = None
                                for idx in range(i+1, j):
                                    if idx < len(doc) and doc[idx].lower_ == "not":
                                        has_not = True
                                        not_token = doc[idx]
                                        break
                                
                                original_chars.extend([
                                    [token.idx, token.idx + len(token.text)],
                                    [next_token.idx, next_token.idx + len(next_token.text)],
                                    [main_verb_token.idx, main_verb_token.idx + len(main_verb_token.text)]
                                ])
                                
                                # 构建副词字符串，保留原始空格
                                adverbs_str = ""
                                for adv in adverbs_between:
                                    # 跳过否定词，因为我们会单独处理
                                    if adv.lower_ != "not":
                                        adverbs_str += adv.text_with_ws
                                
                                # 统一处理逻辑，不再区分否定和非否定情况
                                base = "were " if is_plural else "was "
                                
                                if has_not:
                                    # 如果有否定词，在副词后添加not
                                    if adverbs_str:
                                        replacement = base + adverbs_str + "not "
                                    else:
                                        replacement = base + "not "
                                else:
                                    # 没有否定词，只添加副词
                                    if adverbs_str:
                                        replacement = base + adverbs_str
                                    else:
                                        replacement = base
                                
                                new_tokens[i] = replacement
                                new_tokens[j] = ""  # 移除 'be'
                                
                                # 移除原始的not
                                if has_not and not_token:
                                    not_token_idx = -1
                                    for idx, token in enumerate(doc):
                                        if token is not_token:
                                            not_token_idx = idx
                                            break
                                    if not_token_idx != -1:
                                        new_tokens[not_token_idx] = ""
                                    
                                # 清除will和be之间的副词tokens
                                for idx in range(i+1, j):
                                    new_tokens[idx] = ""
                                    
                                # 暂时记录目标文本的相对位置，后续会更新为绝对位置
                                # 这里只记录相对位置，在生成完整的转换后文本后再计算绝对位置
                                target_start = -1  # 占位，后续更新
                                target_end = -1    # 占位，后续更新
                                target_chars.append([target_start, target_end])
                                
                                # 记录修改信息
                                # 确保original_text和target_text的动词处理方式统一
                                # 如果original_text包含动词，则target_text也应包含动词
                                # 这里original_phrase已经包含了完整的短语（包括动词），所以target_text也应该包含动词
                                convert_infos.append({
                                    "original_char": [original_start, original_end],
                                    "original_text": original_phrase,
                                    "target_char": [target_start, target_end],
                                    "target_text": replacement.strip() + " " + main_verb_token.text
                                })
                                
                                tokens_modified = True
                                i = k + 1
                                continue
                            # 情况 1.2: 进行时态 "will be VBG"
                            elif main_verb_token.tag_ == "VBG":
                                past_form = main_verb_token._.inflect("VBD")
                                if past_form:
                                    # 记录原始短语的起止位置和文本
                                    original_start = token.idx
                                    original_end = main_verb_token.idx + len(main_verb_token.text)
                                    original_phrase = text[original_start:original_end]
                                    
                                    # 检查是否有否定词
                                    has_not = False
                                    not_token = None
                                    for idx in range(i+1, j):
                                        if idx < len(doc) and doc[idx].lower_ == "not":
                                            has_not = True
                                            not_token = doc[idx]
                                            break
                                    
                                    original_chars.extend([
                                        [token.idx, token.idx + len(token.text)],
                                        [next_token.idx, next_token.idx + len(next_token.text)],
                                        [main_verb_token.idx, main_verb_token.idx + len(main_verb_token.text)]
                                    ])
                                    
                                    new_tokens[i] = ""  # 移除 'will'
                                    new_tokens[j] = ""  # 移除 'be'
                                    
                                    # 统一处理逻辑，不再区分否定和非否定情况
                                    if has_not and "not" not in past_form:
                                        # 如果有否定词且past_form不包含否定词，添加not
                                        past_form = "not " + past_form
                                    
                                    # 统一设置token
                                    new_tokens[k] = past_form + main_verb_token.whitespace_
                                    
                                    # 移除原始的not
                                    if has_not and not_token:
                                        not_token_idx = -1
                                        for idx, token in enumerate(doc):
                                            if token is not_token:
                                                not_token_idx = idx
                                                break
                                        if not_token_idx != -1:
                                            new_tokens[not_token_idx] = ""
                                    
                                    # 正确计算目标字符位置 - 基于已构建的文本段
                                    modified_text = "".join(new_tokens[:k+1])
                                    target_start = len(modified_text) - len(past_form + main_verb_token.whitespace_)
                                    target_end = len(modified_text) - len(main_verb_token.whitespace_)
                                    target_chars.append([target_start, target_end])
                                    
                                    # 记录修改信息
                                    # 确保original_text和target_text的动词处理方式统一
                                    # 这里original_phrase已经包含了完整的短语，所以target_text也应该保持一致
                                    convert_infos.append({
                                        "original_char": [original_start, original_end],
                                        "original_text": original_phrase,
                                        "target_char": [target_start, target_end],
                                        "target_text": past_form
                                    })
                                    
                                    tokens_modified = True
                                i = k + 1
                                continue
                            else:
                                # 其他带有动词的情况，当作一般将来时处理
                                is_plural = self._is_subject_plural_for_verb(main_verb_token)
                                original_chars.extend([
                                    [token.idx, token.idx + len(token.text)],
                                    [next_token.idx, next_token.idx + len(next_token.text)]
                                ])
                                
                                replacement = "were " if is_plural else "was "
                                new_tokens[i] = replacement
                                new_tokens[j] = ""  # 移除 'be'
                                
                                # 正确计算目标字符位置 - 基于已构建的文本段
                                modified_text = "".join(new_tokens[:i+1])
                                target_start = len(modified_text) - len(replacement)
                                target_end = len(modified_text)
                                target_chars.append([target_start, target_end])
                                
                                tokens_modified = True
                                i = j + 1
                                continue
                        else:
                            # 情况 1.3: "will be" + 形容词/副词等
                            is_plural = self._is_subject_plural_for_verb(token)
                            # 记录原始短语的起止位置和文本
                            original_start = token.idx
                            original_end = next_token.idx + len(next_token.text)
                            original_phrase = text[original_start:original_end]
                            
                            # 检查是否有否定词
                            has_not = False
                            not_token = None
                            for idx in range(i+1, j):
                                if idx < len(doc) and doc[idx].lower_ == "not":
                                    has_not = True
                                    not_token = doc[idx]
                                    break
                            
                            original_chars.extend([
                                [token.idx, token.idx + len(token.text)],
                                [next_token.idx, next_token.idx + len(next_token.text)]
                            ])
                            
                            # 统一处理逻辑，不再区分否定和非否定情况
                            base = "were " if is_plural else "was "
                            
                            if has_not:
                                # 如果有否定词，添加not
                                replacement = base + "not "
                            else:
                                replacement = base
                            
                            new_tokens[i] = replacement
                            new_tokens[j] = ""  # 移除 'be'
                            
                            # 移除原始的not
                            if has_not and not_token:
                                not_token_idx = -1
                                for idx, token in enumerate(doc):
                                    if token is not_token:
                                        not_token_idx = idx
                                        break
                                if not_token_idx != -1:
                                    new_tokens[not_token_idx] = ""
                            
                            # 暂时记录目标文本的相对位置，后续会更新为绝对位置
                            # 这里只记录相对位置，在生成完整的转换后文本后再计算绝对位置
                            target_start = -1  # 占位，后续更新
                            target_end = -1    # 占位，后续更新
                            target_chars.append([target_start, target_end])
                            
                            # 记录修改信息
                            convert_infos.append({
                                "original_char": [original_start, original_end],
                                "original_text": original_phrase,
                                "target_char": [target_start, target_end],
                                "target_text": replacement.strip()
                            })
                            
                            tokens_modified = True
                            i = j + 1
                            continue

                    # --- 情况 2: 完成时态 "will have VBN" 或 "will have" (have是主要动词) ---
                    elif next_token.lemma_ == "have":
                        k = j + 1
                        while k < len(doc) and doc[k].pos_ not in ("VERB", "AUX"):
                            k += 1

                        # 优先处理 "will have VBN"
                        if k < len(doc) and doc[k].tag_ == "VBN":
                            main_verb_token = doc[k]
                            past_form = main_verb_token._.inflect("VBD")
                            if past_form:
                                original_phrase_end = main_verb_token.idx + len(main_verb_token.text)
                                original_text = doc.text[token.idx:original_phrase_end]

                                # 处理否定词
                                if has_negation:
                                    target_text = f"did not have {past_form}"
                                else:
                                    target_text = f"had {past_form}"

                                # 构建新的tokens
                                new_tokens[i] = ""  # 移除 'will'
                                if has_negation:
                                    new_tokens[j] = "did not have" + main_verb_token.whitespace_
                                else:
                                    new_tokens[j] = "had" + main_verb_token.whitespace_
                                new_tokens[main_verb_token.i] = past_form + main_verb_token.whitespace_

                                # 移除原始的not
                                if has_negation and not_token:
                                    not_token_idx = -1
                                    for idx, t in enumerate(doc):
                                        if t is not_token:
                                            not_token_idx = idx
                                            break
                                    if not_token_idx != -1:
                                        new_tokens[not_token_idx] = ""

                                # 记录修改信息
                                convert_infos.append({
                                    "original_char": [token.idx, original_phrase_end],
                                    "original_text": original_text,
                                    "target_char": [0, 0], # Placeholder, will be updated later
                                    "target_text": target_text
                                })
                                tokens_modified = True
                                i = k + 1
                                continue
                        # 处理 "will have" (have是主要动词)
                        elif next_token.pos_ == "VERB" and next_token.lemma_ == "have":
                            original_start = token.idx
                            original_end = next_token.idx + len(next_token.text)
                            original_phrase = text[original_start:original_end]

                            # 检查是否有否定词
                            has_not = False
                            not_token = None
                            for idx in range(i + 1, j):
                                if idx < len(doc) and doc[idx].lower_ == "not":
                                    has_not = True
                                    not_token = doc[idx]
                                    break

                            # 构建新的tokens
                            new_tokens[i] = ""  # 移除 'will'

                            if has_not:
                                replacement_have = "did not have"
                            else:
                                replacement_have = "had"

                            new_tokens[j] = replacement_have + next_token.whitespace_

                            # 移除原始的not
                            if has_not and not_token:
                                not_token_idx = -1
                                for idx, t in enumerate(doc):
                                    if t is not_token:
                                        not_token_idx = idx
                                        break
                                if not_token_idx != -1:
                                    new_tokens[not_token_idx] = ""

                            # 记录修改信息
                            convert_infos.append({
                                "original_char": [original_start, original_end],
                                "original_text": original_phrase,
                                "target_char": [0, 0], # Placeholder, will be updated later
                                "target_text": replacement_have
                            })
                            tokens_modified = True
                            i = j + 1
                            continue # 继续外层循环

                    # --- 情况 3: 简单将来时 "will VERB" ---
                    elif next_token.pos_ == "VERB":
                        past_form = next_token._.inflect("VBD")
                        if past_form:
                            # 记录原始短语的起止位置和文本
                            original_start = token.idx
                            original_end = next_token.idx + len(next_token.text)
                            original_phrase = text[original_start:original_end]
                            
                            # 检查是否有否定词
                            has_not = False
                            not_token = None
                            for idx in range(i+1, j):
                                if idx < len(doc) and doc[idx].lower_ == "not":
                                    has_not = True
                                    not_token = doc[idx]
                                    break
                            
                            original_chars.extend([
                                [token.idx, token.idx + len(token.text)],
                                [next_token.idx, next_token.idx + len(next_token.text)]
                            ])
                            
                            new_tokens[i] = ""  # 移除 'will'
                            
                            # 统一处理逻辑，不再区分否定和非否定情况
                            if has_not and "not" not in past_form:
                                # 如果有否定词且past_form不包含否定词，添加not
                                past_form = "not " + past_form
                            
                            # 统一设置token
                            new_tokens[j] = past_form + next_token.whitespace_
                            
                            # 移除原始的not
                            if has_not and not_token:
                                not_token_idx = -1
                                for idx, token in enumerate(doc):
                                    if token is not_token:
                                        not_token_idx = idx
                                        break
                                if not_token_idx != -1:
                                    new_tokens[not_token_idx] = ""
                            
                            # 暂时记录目标文本的相对位置，后续会更新为绝对位置
                            # 这里只记录相对位置，在生成完整的转换后文本后再计算绝对位置
                            target_start = -1  # 占位，后续更新
                            target_end = -1    # 占位，后续更新
                            target_chars.append([target_start, target_end])
                            
                            # 记录修改信息
                            convert_infos.append({
                                "original_char": [original_start, original_end],
                                "original_text": original_phrase,
                                "target_char": [target_start, target_end],
                                "target_text": past_form
                            })
                            
                            tokens_modified = True
                        i = j + 1
                        continue

                i += 1

            # --- 检测并转换 "be going to + ..." 结构 (逻辑与 will 类似) ---
            elif token.lemma_ == "be" and i + 2 < len(doc) and \
                    doc[i + 1].text.lower() == "going" and doc[i + 2].text.lower() == "to":
                is_future = True

                j = i + 3
                while j < len(doc) and doc[j].pos_ not in ("VERB", "AUX"):
                    j += 1

                if j < len(doc):
                    next_token = doc[j]

                    # 简单将来时: "be going to VERB" -> "VERB-ed"
                    if next_token.pos_ == "VERB":
                        past_form = next_token._.inflect("VBD")
                        if past_form:
                            # 记录原始短语的起止位置和文本
                            original_start = token.idx
                            original_end = next_token.idx + len(next_token.text)
                            original_phrase = text[original_start:original_end]
                            
                            # 检查是否有否定词
                            has_not = False
                            not_token = None
                            for idx in range(i+1, j):
                                if idx < len(doc) and doc[idx].lower_ == "not":
                                    has_not = True
                                    not_token = doc[idx]
                                    break
                            
                            original_chars.extend([
                                [token.idx, token.idx + len(token.text)],
                                [doc[i+1].idx, doc[i+1].idx + len(doc[i+1].text)],
                                [doc[i+2].idx, doc[i+2].idx + len(doc[i+2].text)],
                                [next_token.idx, next_token.idx + len(next_token.text)]
                            ])
                            
                            new_tokens[i] = ""       # 移除 'be'
                            new_tokens[i + 1] = ""   # 移除 'going'
                            new_tokens[i + 2] = ""   # 移除 'to'
                            
                            # 统一处理逻辑，不再区分否定和非否定情况
                            if has_not and "not" not in past_form:
                                # 如果有否定词且past_form不包含否定词，添加not
                                past_form = "not " + past_form
                            
                            # 统一设置token
                            new_tokens[j] = past_form + next_token.whitespace_
                            
                            # 移除原始的not
                            if has_not and not_token:
                                not_token_idx = -1
                                for idx, token in enumerate(doc):
                                    if token is not_token:
                                        not_token_idx = idx
                                        break
                                if not_token_idx != -1:
                                    new_tokens[not_token_idx] = ""
                            
                            # 暂时记录目标文本的相对位置，后续会更新为绝对位置
                            # 这里只记录相对位置，在生成完整的转换后文本后再计算绝对位置
                            target_start = -1  # 占位，后续更新
                            target_end = -1    # 占位，后续更新
                            target_chars.append([target_start, target_end])
                            
                            # 记录修改信息
                            convert_infos.append({
                                "original_char": [original_start, original_end],
                                "original_text": original_phrase,
                                "target_char": [target_start, target_end],
                                "target_text": past_form
                            })
                            
                            tokens_modified = True
                        i = j + 1
                        continue

                # 如果 "be going to" 后面没有找到可处理的结构，确保 i 前进
                i += 1

            else:
                # 如果当前 token 不构成将来时结构的开头，则正常前进
                i += 1

        # --- 生成转换后的句子 ---
        converted_text = original_text
        if tokens_modified:
            # 清理可能的双重否定
            # 先将new_tokens转换为字符串，然后替换双重否定
            converted_text = "".join(new_tokens)
            # 替换双重否定模式
            converted_text = converted_text.replace("was not not", "was not")
            converted_text = converted_text.replace("were not not", "were not")
            
            # 更新target_char的值，使其指向转换后文本中target_text的实际位置
            # 我们需要一个更精确的方法来确定每个target_text在转换后文本中的位置
            # 不仅按换行符切分，还按句子标点符号切分，以处理大段无换行文本的情况
            
            # 定义句子分隔符
            sentence_delimiters = ['. ', '! ', '? ', '; ']
            
            # 将原始文本和转换后文本按行分割
            original_lines = original_text.split('\n')
            converted_lines = converted_text.split('\n')
            
            # 进一步将每行按句子分割
            original_segments = []
            converted_segments = []
            original_segment_positions = []  # 记录每个段落在原始文本中的起始位置
            converted_segment_positions = []  # 记录每个段落在转换后文本中的起始位置
            
            original_pos = 0
            converted_pos = 0
            
            for line_idx, (orig_line, conv_line) in enumerate(zip(original_lines, converted_lines)):
                # 处理原始行
                orig_line_start = original_pos
                orig_segments = self._split_by_sentence(orig_line, sentence_delimiters)
                
                # 处理转换后行
                conv_line_start = converted_pos
                conv_segments = self._split_by_sentence(conv_line, sentence_delimiters)
                
                # 确保段落数量一致（应该是一致的，因为我们只是替换了时态，不会改变句子结构）
                segment_count = min(len(orig_segments), len(conv_segments))
                
                for seg_idx in range(segment_count):
                    orig_segment = orig_segments[seg_idx]
                    conv_segment = conv_segments[seg_idx]
                    
                    # 计算段落在原始文本中的位置
                    if seg_idx == 0:
                        orig_seg_start = orig_line_start
                    else:
                        orig_seg_start = orig_line_start + sum(len(s) for s in orig_segments[:seg_idx])
                    
                    # 计算段落在转换后文本中的位置
                    if seg_idx == 0:
                        conv_seg_start = conv_line_start
                    else:
                        conv_seg_start = conv_line_start + sum(len(s) for s in conv_segments[:seg_idx])
                    
                    original_segments.append(orig_segment)
                    converted_segments.append(conv_segment)
                    original_segment_positions.append(orig_seg_start)
                    converted_segment_positions.append(conv_seg_start)
                
                # 更新位置
                original_pos += len(orig_line) + 1  # +1 for newline
                converted_pos += len(conv_line) + 1  # +1 for newline
            
            # 对于每个convert_info，找到它在哪个段落中
            for info in convert_infos:
                original_start = info["original_char"][0]
                original_text_segment = info["original_text"]
                target_text_segment = info["target_text"]
                
                # 找到original_start在哪个段落
                segment_index = -1
                for i, pos in enumerate(original_segment_positions):
                    if i < len(original_segment_positions) - 1:
                        next_pos = original_segment_positions[i + 1]
                        if pos <= original_start < next_pos:
                            segment_index = i
                            break
                    else:  # 最后一个段落
                        segment_index = i
                        break
                
                if segment_index != -1 and segment_index < len(converted_segments):
                    # 获取对应的转换后段落
                    converted_segment = converted_segments[segment_index]
                    converted_segment_start = converted_segment_positions[segment_index]
                    
                    # 在这个段落中查找target_text
                    start_pos_in_segment = converted_segment.find(target_text_segment)
                    if start_pos_in_segment != -1:
                        # 计算在整个转换后文本中的绝对位置
                        absolute_start = converted_segment_start + start_pos_in_segment
                        info["target_char"] = [absolute_start, absolute_start + len(target_text_segment)]
                    else:
                        # 如果在当前段落找不到，尝试在相邻的段落中查找
                        found = False
                        for offset in [-1, 1]:
                            neighbor_index = segment_index + offset
                            if 0 <= neighbor_index < len(converted_segments):
                                neighbor_segment = converted_segments[neighbor_index]
                                neighbor_start = converted_segment_positions[neighbor_index]
                                start_pos_in_segment = neighbor_segment.find(target_text_segment)
                                if start_pos_in_segment != -1:
                                    # 计算在整个转换后文本中的绝对位置
                                    absolute_start = neighbor_start + start_pos_in_segment
                                    info["target_char"] = [absolute_start, absolute_start + len(target_text_segment)]
                                    found = True
                                    break
                        
                        if not found:
                            # 如果在相邻段落也找不到，尝试在整个文本中查找
                            start_pos = converted_text.find(target_text_segment)
                            if start_pos != -1:
                                info["target_char"] = [start_pos, start_pos + len(target_text_segment)]
                else:
                    # 如果找不到对应的段落，尝试在整个文本中查找
                    start_pos = converted_text.find(target_text_segment)
                    if start_pos != -1:
                        info["target_char"] = [start_pos, start_pos + len(target_text_segment)]

        # 返回结果
        return {
            "original_text": original_text,
            "target_text": converted_text,
            "is_future_tense": is_future,
            "convertInfos": convert_infos
        }



_converter = None


def _get_converter() -> TenseConverter:
    global _converter
    if _converter is None:
        _converter = TenseConverter()
    return _converter


def convert_future_to_past(text: str) -> dict:
    """对输入文本执行将来时到过去时的转换，返回逐句结果列表。"""
    result = _get_converter().process_text(text)
    return result


