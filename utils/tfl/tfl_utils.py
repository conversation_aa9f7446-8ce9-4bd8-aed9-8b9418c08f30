"""
解析RTF中TFL数据，需要解析出TFL属性(caption, footnote, data, files, etc.)，并且将TFL中的数据、图片上传至OSS。

"""
import re
import os
import json
import subprocess
# from striprtf.striprtf import rtf_to_text
from servers.file_server import upload_and_return_file_info_with_key
from bs4 import BeautifulSoup
from utils.tfl.tfl_tbl_parser import process_rtf
from logger.logger import app_logger
from datetime import datetime

# rtf 文件存储路径
RTF_FILES_FOLDER = 'rtf'
if not os.path.exists(RTF_FILES_FOLDER):
    os.makedirs(RTF_FILES_FOLDER)

def has_images_in_rtf(rtf_file_path: str):
    """
    判断是否为包含图片的RTF文件

    :param rtf_file_path: RTF文件路径
    """
    with open(rtf_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    if re.search(r'\\pict[\s\S]*?(?:\\pngblip|\\jpegblip)', content, re.IGNORECASE):
        return True
    else:
        return False

# def extract_tbl_text_from_rtf(rtf_file_path: str):
#     """
#     从RTF文件中提取表格并返回文本
#
#     :param rtf_file_path: RTF文件路径
#     """
#     with open(rtf_file_path, 'r', encoding='utf-8') as file:
#         rtf_content = file.read()
#     plain_text = rtf_to_text(rtf_content)
#
#     # app_logger.debug(f"plain_text: {plain_text}")
#     # print(f"plain_text: {plain_text}")
#     return plain_text

# def extract_tbl_code(text: str):
#     """
#     提取出TFL标题文本中的表格编号
#
#     :param text: TFL表格标题
#     """
#     match = re.search(r"\d+(?:\.\d+)+", text)
#     return match.group() if match else ''

def extract_tbl_caption(text: str):
    """
    提取出TFL标题文本中的表格名称，常见标题内容：
    - 列表********.3 抗肿瘤病史/既往有意义的操作 – FAS集
    - 表14.1.3 人口统计学和基线特征 - FAS集
    - 图******** 持续缓解时间Kaplan-Meier曲线图 - EAS集
    - 表 14.1.1 受试者分布 全部

    :param text: TFL表格标题
    :return: "列表"、"表"、"图",   "********.3",  描述部分
    """
    app_logger.info(f"before parse caption: {text}")
    aa = text.strip().replace("\n", " ").replace("\xa0", " ")#.replace(" ", "")
    app_logger.info(f"intermediate parse caption: {aa}")

    pattern = r'^(列表|表|图|Table|Figure|List|Listing)\s*(\d+(?:\.\d+)*)\s*(.*)$'
    match = re.match(pattern, aa)
    app_logger.info(f"after parse caption: {match}")
    if match:
        return match.group(1), match.group(2), match.group(3)
    else:
        return '', '', ''


def extract_images_from_rtf(rtf_file_path: str):
    """
    解析图片类型的tfl文件中的数据，需要识别内容和内嵌的图片解析

    :param rtf_file_path: RTF 文件名（在 working_dir 目录下）
    :return: 提取后的html文件所在路径
    """
    # 解析rtf 文件目录
    rtf_dir = os.path.dirname(rtf_file_path)
    rtf_fname = os.path.basename(rtf_file_path)

    # 创建 output_html 子目录
    output_dir = os.path.join(rtf_dir, f'output_html_{datetime.now().strftime("%Y%m%d%H%M%S")}')
    os.makedirs(output_dir, exist_ok=True)

    # 构建输出 HTML 文件路径
    output_html_path = os.path.join(output_dir, "output.html")

    # 执行 unrtf --html 并将输出重定向到 output.html
    with open(output_html_path, 'w', encoding='utf-8') as html_file:
        result = subprocess.run(
            ['unrtf', '--html', '../' + rtf_fname],
            cwd=output_dir,  # 指定工作目录，即image文件的生成路径
            stdout=html_file,  # 将标准输出写入文件
            stderr=subprocess.PIPE,
            text=True
        )
    if result.returncode != 0:
        app_logger.error("执行失败，错误信息：")
        app_logger.error(result.stderr)
    else:
        app_logger.info(f"HTML 文件已成功生成在：{output_html_path}")

    return output_html_path


def extract_images_header(html_path: str):
    """
    提取HTML中的纯文本内容，并自动解码HTML实体（如中文）
    :param html_path: HTML文件路径
    :return: 提取后的纯文本
    """
    with open(html_path, 'r', encoding='utf-8') as html_file:
        html_content = html_file.read()
        soup = BeautifulSoup(html_content, "html.parser")
        text = soup.body.get_text()

    return text.strip()

# def recognize_footnote(text):
#     """
#     抽取脚注和其他部分内容
#     :param text: 原始字符串
#     :return: footnote, the other
#     """
#     # 分割所有片段（保留空行）
#     parts = text.strip().split('|\n')
#
#     # 过滤出非空片段用于判断
#     non_empty_parts = [p.strip() for p in parts if p.strip()]
#
#     if len(non_empty_parts) == 0:
#         return '', text
#
#     # 取最后两个非空部分
#     last = non_empty_parts[-1]
#     second_last = non_empty_parts[-2] if len(non_empty_parts) >= 2 else None
#
#     target_count = 2
#     if second_last is not None and '|' not in second_last:
#         target = f"{second_last}\n{last}"
#     else:
#         target = last
#         target_count = 1
#
#     temp_parts = [p for p in parts]
#     count = 0
#     remove_indices = []
#     for i in range(len(temp_parts) - 1, -1, -1):
#         if temp_parts[i].strip():
#             remove_indices.append(i)
#             count += 1
#             if count == target_count:
#                 break
#
#     if not remove_indices:
#         the_other = text
#     else:
#         new_parts = [
#             part for idx, part in enumerate(temp_parts)
#             if idx not in remove_indices
#         ]
#         # 用 |\n 重新连接
#         the_other = '|\n'.join(new_parts).strip()
#
#     return target.strip(), the_other.strip() if the_other.strip() else the_other

def save_tbl_cells_to_file(rtf_file_path: str, cells: list, rich_content: dict, ooxml_text: str):
    """
    将tfl表格中的数据写入到json文件

    :param rtf_file_path: rtf文件路径
    :param  cells: 单元格数据，二维数组
    :param  rich_content: 带样式的数据（包含caption，footnote，cells），dict
    :param  ooxml_text: ooxml
    :return: json文件的路径
    """
    parent_dir = os.path.dirname(rtf_file_path)
    rtf_name = os.path.basename(rtf_file_path)
    data_dir = os.path.join(parent_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    file_name = f'{rtf_name.replace(".", "-")}.json'
    json_file_path = os.path.join(data_dir, file_name)

    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump({"data": cells, "rich_content": rich_content, 'ooxml_text': ooxml_text}, f, ensure_ascii=False)
    return json_file_path

def upload_cells_to_oss(data_file_path: str, parent_oss_key: str):
    """
    上传tfl data到oss

    :param data_file_path: same with file_key
    :param parent_oss_key: parent_oss_key
    :return: The data parsed from RTF file. If TFL type is T, it should be an 2-d array. Otherwise, it should be an array of picture files.
    """
    file_name = os.path.basename(data_file_path)
    file_key = f'{parent_oss_key}/{file_name}'
    return upload_and_return_file_info_with_key(data_file_path, file_name, file_key)

def upload_images_to_oss(html_path: str, parent_oss_key: str):
    """
    将rtf中解析出的图片上传到oss

    :param html_path: 包含html文件以及图片文件的路径
    :param parent_oss_key: parent_oss_key
    :return: 图片文件在oss中的file info，数组类型
    """
    html_dir = os.path.dirname(html_path)
    jpg_files = []
    for root, dirs, files in os.walk(html_dir):
        for file in files:
            if file.lower().endswith('.jpg'):
                jpg_files.append(os.path.join(root, file))

    file_infos = []
    base_file_key = f'{parent_oss_key}'
    for image_file in jpg_files:
        file_name = os.path.basename(image_file)
        file_key = f'{base_file_key}/{file_name}'
        file_infos.append(upload_and_return_file_info_with_key(image_file, file_name, file_key))

    return file_infos

def post_process_footnote_of_hanlius(footnote: str, category: str, code: str):
    """
    处理复宏汉霖tfl的最后一行，需要将其替换成指定的字符串格式。

    其中如何判断是否为复宏汉霖 tfl，需要check最后一行的内容，样例如下：
        - 中文tfl - [SOURCE: T_ADAE_FREQ.SAS] 28 NOV2022T10:42:45 EDC DATE: 24OCT2022CUT - OFF DATE: 22SEP2022
                    [Source: l_addv.sas] 28NOV2022T10:47:23 EDC DATE: 24OCT2022T0:00:00 CUT-OFF DATE: 22SEP2022
                    [Source:t_adsl_ds.sas] 02DEC2024T13:34:25 EDC DATE: 29NOV20024T14:52:29 ADSL DATE:02DEC2024
        - 英文tfl - [Source: l_ie_crit.sas] 28 NOV2022T14:16:12 EDC DATE: 26NOV2022T16:27:03 CUT - OFF DATE: 01 AUG2022
        - 英文tfl - [Source: t_adsl_ds.sas] 02DEC2024T13:34:25 EDC DATE: 29NOV2024T14:52:29 ADSL DATE: 02DEC2024

    目标字符串格式为：
        - 中文tfl - "数据来源/ Data Source：{category} {code}"
        - 英文tfl - "Data Source：{category} {code}"

    :param footnote: 原始脚注
    :param category: tfl 类型，取值范围：列表|表|图|Table|Figure|List|Listing
    :param code: tfl 表标，例如14.1.1.1
    :return: 处理后的footnote
    """
    print(f"footnote:{footnote}")
    # 判断 category 是否包含中文字符
    has_chinese = bool(re.search(r'[\u4e00-\u9fff]', category))

    if has_chinese:
        target = f"数据来源/ Data Source: {category} {code}"
    else:
        target = f"Data Source: {category} {code}"

    pattern = r'\[Source:\s*[^]]+?\]\s*.*'
    result = re.sub(pattern, target, footnote, flags=re.IGNORECASE)

    return result

def extract_tfl_from_rtf(tfl_file_path: str, original_file_key: str):
    """
    从RTF文件中提取TFL数据，并将数据（表格、图片）上传到OSS，最后返回数据信息

    :param tfl_file_path: RTF文件路径
    :param original_file_key: RTF oss key
    :return: rtf中解析出的tfl_data list
    """
    app_logger.info(f'开始解析rtf - tfl_file_path:{tfl_file_path}, parent_oss_key:{original_file_key}')
    root, extension = os.path.splitext(tfl_file_path)

    # 1. 判断rtf中是否包含图片
    has_image_flag = has_images_in_rtf(tfl_file_path) if extension[1:] == 'rtf' else False

    if has_image_flag:
        # 2.1 抽取rtf中的图片，并将文字部分生成html文件
        output_html_path = extract_images_from_rtf(tfl_file_path)

        # 2.2 解析标题等属性字段
        header = extract_images_header(output_html_path)
        category, code, caption = extract_tbl_caption(header)

        # 2.3 将文件上传到oss
        image_files = upload_images_to_oss(output_html_path, original_file_key)

        # 2.4 组装输出结果
        return {
            "type": "F",
            "category": category,
            "header": header,
            "code": code,
            "caption": caption,
            "files": image_files,
            # "footnote": post_process_footnote_of_hanlius(footnote, category, code),
        }
    else:
        # 2.1 解析表格
        rich_content = process_rtf(tfl_file_path)

        # print(f"---rich_content:{rich_content}")

        # 2.2 解析各个属性字段
        cell_texts = rich_content.get('table_texts')
        header = rich_content.get('caption')
        footnote = rich_content.get('footnote')
        col_count = rich_content.get('col_span')
        row_count = len(rich_content.get('cells'))
        ooxml_text = rich_content.get('ooxml_text')
        category, code, caption = extract_tbl_caption(header)

        app_logger.info(category, code, caption)

        # 2.3 保存数据到json，并上传到oss
        data_file_path = save_tbl_cells_to_file(tfl_file_path, cell_texts, rich_content, ooxml_text)
        oss_file_info = upload_cells_to_oss(data_file_path, original_file_key)

        # 2.4 组装输出结果
        tfl_data = {
            "type": 'T',
            "category": category,
            "header": header,
            "code": code,
            "caption": caption,
            "footnote": post_process_footnote_of_hanlius(footnote, category, code),
            # "cells": cell_texts,
            # "richContent": rich_content,
            "rowCount": row_count,
            "colCount": col_count,
            "files": [oss_file_info]
        }

        return tfl_data

import threading

if __name__ == "__main__":
    # path = "../static/tfl/t14_1_3.rtf"
    # path = "../static/tfl/YXC004_T140101 (1).rtf"  # 正常
    # path = "../static/tfl/oss-local-246-1752581948000-905158.rtf"  # 正常
    # path = "../../static/tfl/t_adae_freq_ctc.rtf"  # 复宏汉霖
    # path = "../../static/tfl/t_adae_freq_ctc.docx"  # 复宏汉霖
    # path = "../static/tfl/t14_1_1_2_test.rtf"  # 正常
    # key = "oss-local-246-1752581953000-078503.rtf"
    # key = 'tfl/test/YXC004_T140101.rtf'
    # key = 'tfl/fhhl/t_adae_freq_ctc_fromdocx.docx'
    # print(extract_tfl_from_rtf(path, key))

    files = [
        # 't14_4_1.rtf',
    # 't14_4_2.rtf',
    # 't14_4_3.rtf',
    # 't14_4_4.rtf',
    # 't14_4_5.rtf'
    #     'henlius/l_ie_crit.rtf' # henlius listing
    #     'henlius/f_pp_boxdlt.rtf'  # henlius figure
    #     'henlius/t_pp_php.rtf'  # henlius table
        'henlius/t_ae_oae_esc.rtf'  # henlius table
             ]

    # threads = []
    for item in files:
        print('------ start -------')
        path = f"../../static/tfl/tfl/{item}"
        key = 'tfl/test/local/' + item
        print(extract_tfl_from_rtf(path, key))

        # t = threading.Thread(target=extract_tfl_from_rtf, args=(path, key))
        # t.start()
        # threads.append(t)

    # 等待所有线程完成
    # for t in threads:
    #     t.join()

    print("All done!")