#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CIOMS 风格 DOCX 填充器（统一版）

合并内容：
1) 首页占位符（数字键、✓/□ 勾选框、属性替换）
2) 附页占位符与动态表格（实验室检查结果、药物反应水平、合并用药、其他研究注册号）
3) \n 换行按“附页逻辑”：先替换文本，随后把含 \n 的段落拆成多个 <w:p> 段落（而不是 <w:br/>）

关键点：
- 统一的占位符映射：包含首页与附页两类键。
- 复选框：占位符值为 'F052'（✓）或 'F0A3'（□），最终在 **拆段落完成后** 统一转换为 <w:sym w:font="Wingdings 2" w:char="..."/>.
- 动态表格：在主文档（word/document.xml）中生成/清空并填充。

CLI:
    python unified_cioms_filler.py --template <模板.docx> --json <payload.json> --out <输出.docx> [--debug]
"""

import json
import os
import re
from typing import Optional, Tuple, List, Dict
from zipfile import ZipFile, ZIP_DEFLATED

try:
    from lxml import etree
except ImportError:
    raise RuntimeError("请先安装 lxml：pip install lxml")

from logger.logger import app_logger

# ----------------------------- 常量与命名空间 -----------------------------
W_NS = "http://schemas.openxmlformats.org/wordprocessingml/2006/main"
XML_NS = "http://www.w3.org/XML/1998/namespace"
NS = {"w": W_NS}


def w(tag: str) -> str:
    """构造带命名空间的 Word 标签名，例如 w('p') -> '{.../main}p'"""
    return f"{{{W_NS}}}{tag}"


class CiomsDocxFiller:
    """
    CIOMS 风格 DOCX 填充器（统一版）
    - 负责处理占位符替换、表格定位与填充、Wingdings2 勾选框等。
    """

    # Wingdings2 勾选框编码
    WINGDINGS_FONT = "Wingdings 2"
    CHECKED = "F052"    # ✓
    UNCHECKED = "F0A3"  # □

    # 默认字体与字号（半磅值）
    DEFAULT_LATIN_FONT = "Times New Roman"
    DEFAULT_EAST_ASIA_FONT = "宋体"
    DEFAULT_FONT_SIZE_HALF_PT = "16"  # 8pt

    def __init__(
        self,
        latin_font: str = None,
        east_asia_font: str = None,
        font_size_half_pt: str = None,
        enable_debug: bool = False
    ):
        """
        :param latin_font: 西文字体（默认 Times New Roman）
        :param east_asia_font: 中文字体（默认 宋体）
        :param font_size_half_pt: 字号（半磅值，默认 16 = 8pt）
        :param enable_debug: 是否启用调试日志（中文）
        """
        self.LATIN_FONT = latin_font or self.DEFAULT_LATIN_FONT
        self.EAST_ASIA_FONT = east_asia_font or self.DEFAULT_EAST_ASIA_FONT
        self.FONT_SIZE_HALF_PT = font_size_half_pt or self.DEFAULT_FONT_SIZE_HALF_PT
        self.debug = enable_debug

    # ============================= 日志工具 =============================
    def _log(self, msg: str):
        if self.debug:
            app_logger.info(msg)

    # ============================= 基础工具：文本/日期/通用 =============================
    def _trim_gap_before_history_heading(self, root: etree._Element) -> int:
        """
        从“其他相关病史（续）/其他相关病史”标题回溯，删除其前面的连续空段。
        既清理标题所在容器内的空段，也清理（若标题在 SDT 内）SDT 节点外侧、紧邻 SDT 之前的空段。
        返回删除的段落数。
        """
        # 1) 找标题（宽松）
        heading = None
        for kw in ("其他相关病史（续）", "其他相关病史"):
            heading = self._find_para_by_text(root, kw)
            if heading is not None:
                break
        if heading is None:
            return 0

        # 供复用的小函数：在给定 container 里，从 node 之前向后清理连续空段
        def sweep_back(container: etree._Element, node_before: etree._Element) -> int:
            removed = 0
            cur = node_before.getprevious()
            while cur is not None:
                if cur.tag == w("p"):
                    # 仅 w:t 构成的文本为空就视为空段；带 <w:numPr>、书签、样式但无文本也算空
                    if not self._is_empty_paragraph(cur):
                        break
                    prev = cur.getprevious()
                    try:
                        container.remove(cur)
                        removed += 1
                    except Exception:
                        break
                    cur = prev
                    continue
                # 碰到表格或另一个 SDT 就停
                if cur.tag in (w("tbl"), w("sdt")):
                    break
                cur = cur.getprevious()
            return removed

        removed_total = 0

        # 2) 先清理标题所在容器内的空段
        sdtc = heading.xpath("ancestor::w:sdtContent[1]", namespaces=NS)
        container = sdtc[0] if sdtc else root.find(".//" + w("body"))
        head_top = self._top_level_child_in_container(heading, container)
        removed_total += sweep_back(container, head_top)

        # 3) 若标题在 SDT 内，再清理 SDT 外侧、紧邻 SDT 之前的空段
        if sdtc:
            sdt_node = sdtc[0].getparent()  # <w:sdt>
            parent_of_sdt = sdt_node.getparent()  # 通常是 <w:body> 或上层容器
            if parent_of_sdt is not None:
                removed_total += sweep_back(parent_of_sdt, sdt_node)

        return removed_total
    def _trim_gap_before_history_heading(self, root: etree._Element) -> int:
        """从“其他相关病史（续）/其他相关病史”标题往前回溯，清理连续的空段。返回删除的段落数。"""
        # 1) 找到目标标题段（宽松匹配）
        heading = None
        for kw in ("其他相关病史（续）", "其他相关病史"):
            heading = self._find_para_by_text(root, kw)
            if heading is not None:
                break
        if heading is None:
            return 0

        # 2) 以标题所在容器为准，定位其在容器下的顶级节点
        sdtc = heading.xpath("ancestor::w:sdtContent[1]", namespaces=NS)
        container = sdtc[0] if sdtc else root.find(".//" + w("body"))
        head_top = self._top_level_child_in_container(heading, container)

        # 3) 反向删除标题前连续的空 paragraph，直到遇到表格/SDT/非空段
        removed = 0
        prev = head_top.getprevious()
        while prev is not None:
            if prev.tag == w("p"):
                if not self._is_empty_paragraph(prev):
                    break
                # 只删纯空段（即便有 <w:numPr>、<w:pPr> 也算空）
                nxt_prev = prev.getprevious()
                try:
                    container.remove(prev)
                    removed += 1
                except Exception:
                    break
                prev = nxt_prev
                continue
            # 遇到表格或 SDT 就停止，避免越界删到上一块内容
            if prev.tag in (w("tbl"), w("sdt")):
                break
            prev = prev.getprevious()
        return removed

    @staticmethod
    def _text(el: etree._Element) -> str:
        """拼接节点下所有 w:t 文本"""
        return "".join(t.text or "" for t in el.findall(".//" + w("t")))

    @staticmethod
    def _norm(s: str) -> str:
        """去空白 + 中/英文冒号，便于匹配“实验室检查结果：”“实验室检查结果 :”"""
        return re.sub(r"[\s:：]+", "", s or "")

    @staticmethod
    def _ascii_cn_runs(text: str) -> List[str]:
        """
        将字符串拆分为“连续英文/数字/符号块”与“非上述字符块”，用于中英混排的 run 切分。
        例如：'ALT 100 U/L异常' -> ['ALT', ' ', '100', ' ', 'U', '/', 'L', '异常']
        """
        if not text:
            return [""]
        return re.findall(r"[A-Za-z0-9\-\./_]+|[^A-Za-z0-9\-\./_]+", str(text))

    @staticmethod
    def _parse_from_to(raw: str) -> Tuple[str, str, str]:
        """
        解析“开始/结束 日期|时间”，宽松且健壮：
        - 支持：YYYY年[MM月[DD日]]、YYYY[-/\.]MM([-/\.]DD)?、仅 YYYY
        - 结束值可为：持续/至今/进行中/ongoing/on-going/未知/unknown/N/A/--
        - 返回：(prefix, start, end)
        """
        if not raw:
            return "", "", ""

        s = str(raw).strip()
        s_norm = s.replace("：", ":")  # 统一冒号

        # 日期模式（非捕获）
        date_cn_loose = r"\d{4}年(?:\d{1,2}月(?:\d{1,2}日)?)?"
        date_iso_loose = r"\d{4}(?:[-/\.]\d{1,2}(?:[-/\.]\d{1,2})?)?"
        date_alt = rf"(?:{date_cn_loose}|{date_iso_loose})"

        # 允许的“结束”文字值
        end_word = r"(?:持续|至今|进行中|ongoing|on-going|未知|unknown|n/?a|N/?A|--|-)"

        pat = re.compile(
            rf"""
            (?P<prefix>.*?)
            (?:
                开始(?:日期|时间)|起始(?:日期|时间)|Start(?:\s*Date)?
            )\s*:?\s*
            (?P<start>{date_alt})
            .*?
            (?:
                结束(?:日期|时间)|截止(?:日期|时间)|End(?:\s*Date)?
            )\s*:?\s*
            (?P<end>{date_alt}|{end_word})
            """,
            re.I | re.S | re.X,
        )

        m = pat.search(s_norm)
        if m:
            prefix = m.group("prefix") or ""
            start = m.group("start") or ""
            end = m.group("end") or ""
        else:
            # 回退：尽力抓两个日期；一个落到 start，另一个落到 end
            dd = re.findall(date_alt, s_norm)
            prefix = ""
            start = dd[0] if len(dd) >= 1 else ""
            end = dd[1] if len(dd) >= 2 else ""
            if not end:
                low = s_norm.lower()
                for tok in [
                    "持续",
                    "至今",
                    "进行中",
                    "ongoing",
                    "on-going",
                    "未知",
                    "unknown",
                    "n/a",
                    "na",
                    "--",
                    "-",
                ]:
                    if tok.lower() in low:
                        end = tok
                        break

        def _trim(v: str) -> str:
            # 去掉首尾常见分隔符和零宽字符
            v = re.sub(r"[\u200b\u200c\u200d\uFEFF]", "", v or "")
            return v.strip(" \t\r\n/-.,，。")

        return _trim(prefix), _trim(start), _trim(end)

    def _norm_cn_date(self, s: str) -> str:
        """
        归一化日期到中文格式：
        - "//2019" 或 "2019" -> "2019年"
        - "2023-09-05" / "2023/9/5" / "2023.9.5" -> "2023年9月5日"
        - 已含“年/月/日”的原样返回
        """
        if not s:
            return ""
        s = str(s).strip()
        if "年" in s or "月" in s or "日" in s:
            return s

        m = re.match(r"^/?/?\s*(\d{4})\s*$", s)
        if m:
            return f"{m.group(1)}年"

        m = re.match(r"^\s*(\d{4})[-/\.](\d{1,2})(?:[-/\.](\d{1,2}))?\s*$", s)
        if m:
            y, mo, d = m.group(1), int(m.group(2)), (m.group(3) and int(m.group(3)))
            return f"{y}年{mo}月{d}日" if d else f"{y}年{mo}月"

        return s
    # ============================= XML 片段构造/修改工具 =============================
    @staticmethod
    def _set_tr_height(tr: etree._Element, height_twips: int):
        """为表格行 <w:tr> 设置“精确高度”"""
        trPr = tr.find(w("trPr"))
        if trPr is None:
            trPr = etree.SubElement(tr, w("trPr"))

        trHeight = trPr.find(w("trHeight"))
        if trHeight is None:
            trHeight = etree.SubElement(trPr, w("trHeight"))

        trHeight.set(w("val"), str(height_twips))
        trHeight.set(w("hRule"), "exact")
    def _sanitize_free_text(self, s: str) -> str:
        if not s:
            return s
        lines = s.splitlines()
        cleaned, skip = [], False
        for ln in lines:
            n = ln.strip()
            # 过滤 markdown 表格行
            if n.startswith("|") and n.endswith("|"):
                continue
            if set(n) <= set("-:| "):
                continue
            # 跳过“24.其他研究注册号”这段意外混入的标题块
            if re.match(r"^\s*24[\.\）]?\s*其他研究注册号", n):
                skip = True
                continue
            if skip:
                if n == "" or re.match(r"^\s*\d+[\.\）]", n):
                    skip = False
                    if n:
                        cleaned.append(ln)
                continue
            cleaned.append(ln)
        return "\n".join(cleaned)

    def _strip_ocr_leading_colon(self, s: str) -> str:
        """去掉值开头误识别的全/半角冒号及其空格：'：xxx' / ': xxx' -> 'xxx'"""
        if s is None:
            return ""
        s = str(s)
        return re.sub(r"^\s*[:：]+\s*", "", s)

    def _s(self, v) -> str:
        """
        安全字符串：统一成 str，去掉前后空白和零宽字符，防止 XML 写入时“看起来有字，实际是不可见字符”的情况。
        """
        s = "" if v is None else str(v)
        # 去掉零宽字符
        s = re.sub(r"[\u200b\u200c\u200d\uFEFF]", "", s)
        return s.strip()
    def _make_r(self, text: str) -> etree._Element:
        """创建一个 run（w:r），带默认字体与字号"""
        r = etree.Element(w("r"))
        rPr = etree.SubElement(r, w("rPr"))
        rFonts = etree.SubElement(rPr, w("rFonts"))
        rFonts.set(w("ascii"), self.LATIN_FONT)
        rFonts.set(w("hAnsi"), self.LATIN_FONT)
        rFonts.set(w("eastAsia"), self.EAST_ASIA_FONT)
        rFonts.set(w("cs"), self.LATIN_FONT)
        sz = etree.SubElement(rPr, w("sz"))
        sz.set(w("val"), self.FONT_SIZE_HALF_PT)
        szCs = etree.SubElement(rPr, w("szCs"))
        szCs.set(w("val"), self.FONT_SIZE_HALF_PT)

        t = etree.SubElement(r, w("t"))
        if text and (text[0] == " " or text[-1] == " "):
            t.set(f"{{{XML_NS}}}space", "preserve")
        t.text = "" if text is None else str(text)
        return r

    def _make_p(self, *runs_text: str) -> etree._Element:
        """创建一个段落（w:p），按“中英 run 拆分”写入内容"""
        p = etree.Element(w("p"))
        pPr = etree.SubElement(p, w("pPr"))
        spacing = etree.SubElement(pPr, w("spacing"))
        spacing.set(w("before"), "48")
        spacing.set(w("beforeLines"), "20")
        spacing.set(w("after"), "0")
        spacing.set(w("line"), "264")
        spacing.set(w("lineRule"), "auto")
        jc = etree.SubElement(pPr, w("jc"))
        jc.set(w("val"), "both")

        for seg in runs_text:
            p.append(self._make_r(seg))
        return p

    def _set_cell_text(self, tc: etree._Element, text: str, split_en_cn: bool = True):
        """重置单元格文本"""
        for p in list(tc):
            tc.remove(p)
        parts = self._ascii_cn_runs(text) if split_en_cn else [text]
        tc.append(self._make_p(*parts))

    def _new_tc(self, width_pct: int, text: str = "", gridspan: int = None, split_en_cn: bool = True) -> etree._Element:
        """新建单元格（带宽度、可选跨列），并写入文本"""
        tc = etree.Element(w("tc"))
        tcPr = etree.SubElement(tc, w("tcPr"))
        tcW = etree.SubElement(tcPr, w("tcW"))
        tcW.set(w("w"), str(width_pct))
        tcW.set(w("type"), "pct")
        if gridspan:
            gs = etree.SubElement(tcPr, w("gridSpan"))
            gs.set(w("val"), str(gridspan))
        shd = etree.SubElement(tcPr, w("shd"))
        shd.set(w("val"), "clear")
        shd.set(w("color"), "auto")
        shd.set(w("fill"), "auto")
        self._set_cell_text(tc, text, split_en_cn=split_en_cn)
        return tc

    @staticmethod
    def _new_tr(*tcs: etree._Element) -> etree._Element:
        """新建表格行"""
        tr = etree.Element(w("tr"))
        for tc in tcs:
            tr.append(tc)
        return tr

    @staticmethod
    def _ensure_tbl_pr_standard(tbl: etree._Element, look: bool = True):
        """标准化表格属性（宽度、布局、边距、表外观）"""
        tblPr = tbl.find(w("tblPr"))
        if tblPr is None:
            tblPr = etree.SubElement(tbl, w("tblPr"))
        else:
            for child in list(tblPr):
                tblPr.remove(child)

        ov = etree.SubElement(tblPr, w("tblOverlap"))
        ov.set(w("val"), "never")
        tblW = etree.SubElement(tblPr, w("tblW"))
        tblW.set(w("w"), "5000")
        tblW.set(w("type"), "pct")
        layout = etree.SubElement(tblPr, w("tblLayout"))
        layout.set(w("type"), "fixed")
        cellMar = etree.SubElement(tblPr, w("tblCellMar"))
        left = etree.SubElement(cellMar, w("left"))
        left.set(w("w"), "107")
        left.set(w("type"), "dxa")
        right = etree.SubElement(cellMar, w("right"))
        right.set(w("w"), "107")
        right.set(w("type"), "dxa")

        if look:
            tblLook = etree.SubElement(tblPr, w("tblLook"))
            tblLook.set(w("val"), "04A0")
            tblLook.set(w("firstRow"), "1")
            tblLook.set(w("lastRow"), "0")
            tblLook.set(w("firstColumn"), "1")
            tblLook.set(w("lastColumn"), "0")
            tblLook.set(w("noHBand"), "0")
            tblLook.set(w("noVBand"), "1")

    @staticmethod
    def _reset_tbl_grid(tbl: etree._Element, widths_twips: List[int]):
        """重建 tblGrid"""
        grid = tbl.find(w("tblGrid"))
        if grid is None:
            grid = etree.SubElement(tbl, w("tblGrid"))
        else:
            for c in list(grid):
                grid.remove(c)
        for width in widths_twips:
            col = etree.SubElement(grid, w("gridCol"))
            col.set(w("w"), str(width))

    @staticmethod
    def _strip_all_tblpPr(root: etree._Element):
        """移除所有表格 tblpPr（避免表格浮动带来的偏移问题）"""
        for tblPr in root.findall(".//" + w("tblPr")):
            tp = tblPr.find("./" + w("tblpPr"))
            if tp is not None:
                tblPr.remove(tp)

    # ============================= SDT/表格/段落 定位工具 =============================
    @staticmethod
    def _sdt_has_name(sdt: etree._Element, name: str) -> bool:
        """判断内容控件（w:sdt）是否具有给定 tag 或 alias 名称"""
        if not name:
            return False
        pr = sdt.find(w("sdtPr"))
        if pr is None:
            return False
        tag = pr.find(w("tag"))
        alias = pr.find(w("alias"))
        return (tag is not None and tag.get(w("val")) == name) or (alias is not None and alias.get(w("val")) == name)

    def _find_tbl_by_sdt_name(self, root: etree._Element, name: str) -> Optional[etree._Element]:
        """
        按 SDT 名称（tag/alias）定位表：
        - 若 sdt 内有表则直接返回
        - 否则从该 sdt 起向后找，直到遇到“下一个 sdt”或文末，遇表即返回
        """
        sdts = root.findall(".//" + w("sdt"))
        for sdt in sdts:
            if not self._sdt_has_name(sdt, name):
                continue

            inner_tbl = sdt.find(".//" + w("tbl"))
            if inner_tbl is not None:
                return inner_tbl

            parent = sdt.getparent()
            if parent is None:
                continue
            siblings = list(parent)
            i = siblings.index(sdt)
            j = i + 1
            while j < len(siblings):
                if siblings[j].tag == w("sdt"):
                    break
                if siblings[j].tag == w("tbl"):
                    return siblings[j]
                j += 1
        return None

    def _find_tbl_by_header_texts(self, root: etree._Element, must_have_texts: List[str]) -> Optional[etree._Element]:
        """兜底：扫描所有表，若首行拼接文本能包含 must_have_texts 的每个元素，则判定为目标表。"""
        for tbl in root.findall(".//" + w("tbl")):
            first_tr = tbl.find(w("tr"))
            if first_tr is None:
                continue
            head_text = []
            for tc in first_tr.findall(w("tc")):
                txt = "".join(tc.itertext())
                head_text.append((txt or "").strip())
            head_joined = " ".join(head_text)
            if all(k in head_joined for k in must_have_texts):
                return tbl
        return None

    @staticmethod
    def _prev_visible_p(el: etree._Element) -> Optional[etree._Element]:
        """向前寻找最近的段落（w:p），找不到则返回 None"""
        p = el.getprevious()
        while p is not None and p.tag != w("p"):
            p = p.getprevious()
        return p

    def _locate_lab_results_table(self, root: etree._Element) -> Optional[etree._Element]:
        """
        定位“实验室检查结果”表（优先级）：
        1) SDT 名称 'lab_results_table'
        2) 表头包含 “检查名称/检查日期/检查结果/正常值”，且其前一段落标题包含“实验室检查结果”
        3) 段落文本包含“实验室检查结果”，取其后的第一个表
        """
        tbl = self._find_tbl_by_sdt_name(root, "lab_results_table")
        if tbl is not None:
            return tbl

        must = ["检查名称", "检查日期", "检查结果", "正常值"]
        for t in root.findall(".//" + w("tbl")):
            first_tr = t.find(w("tr"))
            if first_tr is None:
                continue
            cells = first_tr.findall(w("tc"))
            if len(cells) < 4:
                continue
            header_texts = ["".join(tc.itertext()).strip() for tc in cells]
            if all(any(kw in h for h in header_texts) for kw in must):
                prev_p = self._prev_visible_p(t)
                if prev_p is None:
                    return t
                title = self._norm(self._text(prev_p))
                if "实验室检查结果" in title:
                    return t

        for p in root.findall(".//" + w("p")):
            if "实验室检查结果" in self._norm(self._text(p)):
                sib = p.getnext()
                while sib is not None and sib.tag != w("tbl"):
                    sib = sib.getnext()
                if sib is not None:
                    return sib
        return None

    @staticmethod
    def _find_para_by_text(root: etree._Element, keyword: str) -> Optional[etree._Element]:
        """返回第一个包含 keyword 的段落（宽松匹配：忽略 run 分割与空白）"""
        for p in root.findall(".//" + w("p")):
            txt = "".join(t.text or "" for t in p.findall(".//" + w("t")))
            if txt and keyword in txt:
                return p
        return None

    @staticmethod
    def _top_level_child_in_container(node: etree._Element, container: etree._Element) -> etree._Element:
        """返回 node 在 container 下的顶级子节点，用于在其后插入新元素"""
        cur = node
        while cur is not None and cur.getparent() is not None and cur.getparent() is not container:
            cur = cur.getparent()
        return cur if cur is not None and cur.getparent() is container else node

    def _locate_container_and_tbl(
        self,
        anchor: etree._Element
    ) -> Tuple[etree._Element, etree._Element, Optional[etree._Element], etree._Element, int, bool]:
        """
        为“合并用药”构建替换/插入位置：
        返回 (container, anchor_top, old_tbl, hold, idx, in_sdt)
        """
        sdt_content = anchor.xpath("ancestor::w:sdtContent[1]", namespaces=NS)
        in_sdt = bool(sdt_content)
        container = sdt_content[0] if in_sdt else anchor.getroottree().find(".//" + w("body"))
        anchor_top = self._top_level_child_in_container(anchor, container)

        sib = anchor_top.getnext()
        while sib is not None and sib.tag not in (w("tbl"), w("sdt")):
            sib = sib.getnext()
        target = sib

        if target is None:
            after = container.xpath(
                "./*[count(preceding-sibling::*) > count(preceding-sibling::*[. = $anchor_top]) and (self::w:tbl or self::w:sdt)]",
                anchor_top=anchor_top, namespaces=NS
            )
            target = after[0] if after else None

        hold = container
        old_tbl = None
        idx = None

        if target is not None and target.tag == w("tbl"):
            old_tbl = target
            idx = hold.index(old_tbl)
        elif target is not None and target.tag == w("sdt"):
            sdtc = target.find(".//" + w("sdtContent"))
            if sdtc is not None:
                inner_tbl = sdtc.find(".//" + w("tbl"))
                if inner_tbl is not None:
                    hold = sdtc
                    old_tbl = inner_tbl
                    idx = hold.index(old_tbl)
                else:
                    hold = sdtc
                    idx = 0
            else:
                hold = container
                idx = container.index(anchor_top) + 1
        else:
            idx = container.index(anchor_top) + 1

        return container, anchor_top, old_tbl, hold, idx, in_sdt

    # ============================= json 为空，去掉对应的 xml 片段 ===================
    def _is_empty_paragraph(self, p_el: etree._Element) -> bool:
        """该段落拼接文本是否为空（忽略空白）。"""
        txt = self._text(p_el)
        return (txt is None) or (txt.strip() == "")

    def _remove_heading_and_next_empty_para(
        self, root: etree._Element, heading_pattern: str
    ) -> bool:
        """
        删除“标题段落 + 紧随其后的空段落（占位符被替换为空后）”。
        heading_pattern：用于匹配标题段落的关键字（宽松匹配，忽略空白和中/英文冒号）。
        """
        target = self._norm(heading_pattern)
        for p in root.xpath(".//w:p", namespaces=NS):
            if target in self._norm(self._text(p)):
                parent = p.getparent()
                if parent is None:
                    continue
                # 找到后一个兄弟段落
                sib = p.getnext()
                while sib is not None and sib.tag != w("p"):
                    sib = sib.getnext()
                if sib is not None and self._is_empty_paragraph(sib):
                    parent.remove(sib)
                    parent.remove(p)
                    return True
        return False

    def _remove_next_empty_para_only(
        self, root: etree._Element, heading_pattern: str
    ) -> bool:
        """
        删除“标题段落后紧随的空段落”，但保留标题本身。
        heading_pattern：用于匹配标题段落的关键字（宽松匹配，忽略空白和中/英文冒号）。
        """
        target = self._norm(heading_pattern)
        for p in root.xpath(".//w:p", namespaces=NS):
            if target in self._norm(self._text(p)):
                # 找到后一个兄弟段落
                sib = p.getnext()
                while sib is not None and sib.tag != w("p"):
                    sib = sib.getnext()
                if sib is not None and self._is_empty_paragraph(sib):
                    parent = p.getparent()
                    if parent is not None:
                        parent.remove(sib)
                        return True
                return False
        return False

    def _prune_empty_sections(
        self, root: etree._Element, placeholder_map: Dict[str, str], payload: dict
    ):
        """
        根据 payload 值是否为空，删除对应“标题 + 占位符”两行。
        只处理文本段落块；合并用药的表格在 fill_concomitant_drugs 里单独处理。
        """
        # 1a.国家 / {{国家续表}}
        if not (placeholder_map.get("国家续表") or "").strip():
            self._remove_heading_and_next_empty_para(root, "1a.国家")

        # ✅ 新增：当“反应描述续表”为空，仅删除占位符所在空段，不删除标题行
        if not (placeholder_map.get("反应描述续表") or "").strip():
            self._remove_next_empty_para_only(
                root, "7+13反应描述（包括相关检查/实验室数据）（续）"
            )

        # 15.日剂量（续） / {{日剂量}}
        if not (placeholder_map.get("日剂量") or "").strip():
            # 为了更稳妥，匹配完整标题文本
            self._remove_heading_and_next_empty_para(root, "15.日剂量（续）")

        # 23.其他相关病史（续） / {{其他相关病史}}
        if not (placeholder_map.get("其他相关病史") or "").strip():
            self._remove_heading_and_next_empty_para(root, "其他相关病史（续）")

        # 病史和并发疾病： / {{病史和并发疾病}}
        if not (placeholder_map.get("病史和并发疾病") or "").strip():
            # 标题里有全角冒号，_norm 会忽略冒号，所以用核心关键词即可
            self._remove_heading_and_next_empty_para(root, "病史和并发疾病")

        # ✅ 新增：药物警戒评论： / {{药物警戒评论}}
        # 当 pharmacovigilance_comments 为空时，删除“药物警戒评论：”标题段 + 紧随其后的空段
        if not (placeholder_map.get("药物警戒评论") or "").strip():
            self._remove_heading_and_next_empty_para(root, "药物警戒评论")
        # —— 实验室检查结果（文本块）——
        # 当“检查结果-文本”为空，删除“检查结果（代码）/结果非结构化数据（自由文本）：”标题 + 紧随空段
        if not (placeholder_map.get("检查结果-文本") or "").strip():
            self._remove_heading_and_next_empty_para(root, "检查结果（代码）/结果非结构化数据（自由文本）")
        # —— 报告者给出的反应描述 ——
        # 当 reaction_description_as_per_reporter 为空：删除“报告者给出的反应描述：”标题段 + 紧随其后的空段
        if not (placeholder_map.get("报告者给出的反应描述") or "").strip():
            self._remove_heading_and_next_empty_para(root, "报告者给出的反应描述")
        # —— 实验室检查结果（表格块）——
        # 当 lab_results_table 为空：删除“实验室检查结果：”标题段 + 紧随其后的表格
        if not ((payload or {}).get("lab_results_table") or []):
            anchor = self._find_para_by_text(root, "实验室检查结果")
            if anchor is not None:
                try:
                    container, anchor_top, old_tbl, hold, idx, in_sdt = self._locate_container_and_tbl(anchor)
                    if old_tbl is not None:
                        try:
                            hold.remove(old_tbl)
                            self._log("【实验室检查结果】无数据：已删除标题后旧表。")
                        except Exception as _:
                            pass
                    parent = anchor_top.getparent()
                    if parent is not None:
                        parent.remove(anchor_top)
                        self._log("【实验室检查结果】无数据：已删除“实验室检查结果”标题段。")
                except Exception as _:
                    # 忽略异常，避免影响其他裁剪
                    pass

    # ============================= 段落与属性层面的替换 =============================
    @staticmethod
    def _set_text_with_linebreaks(run_el: etree._Element, t_el: etree._Element, text: str):
        """
        将文本写入 <w:t> 节点。
        不将 \n 转换为 <w:br/>，而是原样保留，后续统一拆分段落。
        """
        t_el.text = text
        if text and (text.startswith(" ") or text.endswith(" ")):
            t_el.set(f"{{{XML_NS}}}space", "preserve")

        # 清理本 run 内的多余 w:br 与多余 w:t，仅保留第一个 w:t
        children_to_remove = []
        is_first_t = True
        for child in run_el:
            if child.tag == w("br"):
                children_to_remove.append(child)
            elif child.tag == w("t"):
                if is_first_t:
                    is_first_t = False
                else:
                    children_to_remove.append(child)
        for child in children_to_remove:
            run_el.remove(child)

    def _split_paragraphs_with_newlines(self, root: etree._Element):
        """
        遍历所有段落，若其文本含 '\n' 则将其拆分为多个段落。
        注意：拆分时会复制原段落的 pPr，且每一行按中英块分 run 以维持字体设置。
        """
        all_paragraphs = list(root.xpath(".//w:p", namespaces=NS))
        for p_el in all_paragraphs:
            full_text = self._text(p_el)
            if "\n" not in full_text:
                continue

            parent = p_el.getparent()
            if parent is None:
                continue

            p_props = p_el.find(w("pPr"))
            try:
                p_index = parent.index(p_el)
            except ValueError:
                continue

            lines = full_text.split("\n")

            parent.remove(p_el)

            for i, line in enumerate(lines):
                if not line and i == len(lines) - 1:
                    continue
                new_p = etree.Element(w("p"))
                if p_props is not None:
                    new_p.append(etree.fromstring(etree.tostring(p_props)))
                text_parts = self._ascii_cn_runs(line)
                for part in text_parts:
                    new_p.append(self._make_r(part))
                parent.insert(p_index + i, new_p)

    def _convert_wingdings_codes_to_sym_in_paragraph(self, p_el: etree._Element):
        """
        将段落内的 'F052' / 'F0A3' 文本（可能跨 run）替换为 <w:sym w:font="Wingdings 2" w:char="F052|F0A3" />
        """
        TOKENS = [self.CHECKED, self.UNCHECKED]
        while True:
            t_nodes = p_el.xpath(".//w:r/w:t", namespaces=NS)
            if not t_nodes:
                return

            texts = [t.text or "" for t in t_nodes]
            S = "".join(texts)

            first_pos = None
            first_tok = None
            for tok in TOKENS:
                pos = S.find(tok)
                if pos != -1 and (first_pos is None or pos < first_pos):
                    first_pos, first_tok = pos, tok
            if first_pos is None:
                return

            starts = []
            s = 0
            for txt in texts:
                starts.append(s)
                s += len(txt)
            ends = [starts[i] + len(texts[i]) for i in range(len(t_nodes))]

            pos = first_pos
            pos_end = first_pos + len(first_tok)
            start_i = next((i for i in range(len(t_nodes)) if starts[i] <= pos < ends[i]), None)
            end_i = next((j for j in range(len(t_nodes)) if starts[j] < pos_end <= ends[j]), None)
            if start_i is None or end_i is None:
                return

            start_t = t_nodes[start_i]
            start_run = start_t.getparent()
            start_txt = texts[start_i]
            left_len = pos - starts[start_i]
            left = start_txt[:left_len]

            end_t = t_nodes[end_i]
            end_txt = texts[end_i]
            consumed_in_end = pos_end - starts[end_i]
            right_end = end_txt[consumed_in_end:]

            start_t.set(f"{{{XML_NS}}}space", "preserve")
            start_t.text = left

            if start_i != end_i:
                for k in range(start_i + 1, end_i):
                    tk = t_nodes[k]
                    tk.set(f"{{{XML_NS}}}space", "preserve")
                    tk.text = ""
                end_t.set(f"{{{XML_NS}}}space", "preserve")
                end_t.text = right_end

            siblings = list(start_run)
            try:
                idx = siblings.index(start_t)
            except ValueError:
                idx = len(siblings) - 1

            sym = etree.Element(w("sym"))
            sym.set(w("font"), self.WINGDINGS_FONT)
            sym.set(w("char"), first_tok)
            start_run.insert(idx + 1, sym)

            if start_i == end_i:
                right_in_same = start_txt[left_len + len(first_tok):]
                if right_in_same:
                    new_t = etree.Element(w("t"))
                    new_t.set(f"{{{XML_NS}}}space", "preserve")
                    new_t.text = right_in_same
                    start_run.insert(idx + 2, new_t)

    def _convert_all_wingdings(self, root: etree._Element):
        """在整个 XML 树上统一把 F052/F0A3 文本转换为 <w:sym>（放在拆段落之后执行）"""
        for p in root.xpath(".//w:p", namespaces=NS):
            self._convert_wingdings_codes_to_sym_in_paragraph(p)

    def _replace_in_paragraph(self, p_el: etree._Element, placeholder_map: Dict[str, str]):
        """
        在段落层面做占位符替换：
        - 先把本段所有 w:t 串联为一个字符串
        - 对 "{{KEY}}" 做统一替换
        - 再按原各 t 的长度把新文本切回去（保持 run 样式）
        - \n 不转 <w:br/>，而是先保留，待后续统一拆段落
        """
        t_nodes = p_el.xpath(".//w:t", namespaces=NS)
        if not t_nodes:
            return

        original = [(t, t.text or "") for t in t_nodes]
        lens = [len(s) for _, s in original]
        flat = "".join(s for _, s in original)
        new_flat = flat

        if "{{" in flat and "}}" in flat and placeholder_map:
            for key, val in placeholder_map.items():
                tok = "{{" + key + "}}"
                if tok in new_flat:
                    new_flat = new_flat.replace(tok, val)

        changed = (new_flat != flat)
        if not changed:
            for t, txt in original:
                if "\n" in txt:
                    self._set_text_with_linebreaks(t.getparent(), t, txt)
            return

        pos = 0
        n = len(t_nodes)
        for i, (t, _) in enumerate(original):
            if i < n - 1:
                take = lens[i]
                seg = new_flat[pos:pos + take]
                pos += take
            else:
                seg = new_flat[pos:]
            self._set_text_with_linebreaks(t.getparent(), t, seg)

    def _replace_in_attributes(self, root: etree._Element, placeholder_map: Dict[str, str]):
        """
        属性值替换：扫描所有元素属性，将 "{{KEY}}" 替换为相应值。
        同时修正 <w:sym> 的字体为 Wingdings 2（若缺失或为 Wingdings/Symbol）。
        """
        if not placeholder_map:
            for sym in root.xpath(".//w:sym", namespaces=NS):
                if sym.get(w("font")) in (None, "", "Wingdings", "Symbol"):
                    sym.set(w("font"), self.WINGDINGS_FONT)
            return

        for el in root.iter():
            if not el.attrib:
                continue
            new_attrib = {}
            modified = False
            for k, v in el.attrib.items():
                new_v = v
                if "{{" in v and "}}" in v:
                    for key, val in placeholder_map.items():
                        tok = "{{" + key + "}}"
                        if tok in new_v:
                            new_v = new_v.replace(tok, val)
                            modified = True
                new_attrib[k] = new_v
            if modified:
                el.attrib.clear()
                el.attrib.update(new_attrib)

        # <w:sym> 默认字体修正
        for sym in root.xpath(".//w:sym", namespaces=NS):
            if sym.get(w("font")) in (None, "", "Wingdings", "Symbol"):
                sym.set(w("font"), self.WINGDINGS_FONT)

    # ============================= JSON 占位符映射：统一版 =============================
    @staticmethod
    def _resolve(obj: dict, keys: List[str]):
        cur = obj
        for k in keys:
            if not isinstance(cur, dict) or k not in cur:
                return None
            cur = cur[k]
        return cur

    def _jstr(self, obj: dict, keys: List[str], default: str = "") -> str:
        v = self._resolve(obj, keys)
        if v is None:
            return default
        if isinstance(v, dict) and "value" in v:
            v = v["value"]
        return "" if v is None else str(v)

    def _jbool(self, obj: dict, keys: List[str], default: bool = False) -> bool:
        v = self._resolve(obj, keys)
        if isinstance(v, dict) and "continued" in v:
            v = v["continued"]
        if isinstance(v, bool):
            return v
        if isinstance(v, str):
            return v.strip().lower() in ("1", "true", "yes", "y")
        if v is None:
            return default
        return bool(v)

    @staticmethod
    def chk(b: bool) -> str:
        """true => CHECKED(✓ F052), false => UNCHECKED(□ F0A3)"""
        return CiomsDocxFiller.CHECKED if b else CiomsDocxFiller.UNCHECKED

    @staticmethod
    def cont_mark(b: bool) -> str:
        """Return '续' if continued=True, else empty string."""
        return "续" if b else ""

    def _build_placeholder_map_home(self, payload: dict) -> Dict[str, str]:
        """首页（数字键）占位符映射"""
        m = {}

        # 顶层
        m["id"] = self._jstr(payload, ["report_id"])

        # ✅ 新增：页眉编号（用于 header2.xml 里的 {{页眉编号}}）
        # 主要用 report_id；若没有则回退用 24b（mfr_control_no）
        m["页眉编号"] = self._jstr(payload, ["report_id"]) or self._jstr(
            payload, ["manufacturer_information", "mfr_control_no"]
        )

        # I. 反应信息
        m["1"] = self._jstr(payload, ["reaction_information", "patient_initials"])
        m["1a"] = self._jstr(payload, ["reaction_information", "country"])
        m["1a续"] = self.cont_mark(self._jbool(payload, ["reaction_information", "country", "continued"]))

        m["2-1"] = self._jstr(payload, ["reaction_information", "date_of_birth", "year"])
        m["2-2"] = self._jstr(payload, ["reaction_information", "date_of_birth", "month"])
        m["2-3"] = self._jstr(payload, ["reaction_information", "date_of_birth", "day"])
        m["2a"] = self._jstr(payload, ["reaction_information", "age"])
        m["3"] = self._jstr(payload, ["reaction_information", "sex"])

        m["4"] = self._jstr(payload, ["reaction_information", "reaction_onset", "year"])
        m["5"] = self._jstr(payload, ["reaction_information", "reaction_onset", "month"])
        m["6"] = self._jstr(payload, ["reaction_information", "reaction_onset", "day"])

        m["7+13"] = self._jstr(payload, ["reaction_information", "describe_reaction"])
        m["7+13续"] = self.cont_mark(self._jbool(payload, ["reaction_information", "describe_reaction", "continued"]))

        # 8-x （CHECK ALL APPROPRIATE）
        m["8-1"] = self.chk(self._jbool(payload, ["reaction_information", "check_all_appropriate", "patient_died"]))
        m["8-2"] = self.chk(self._jbool(payload, ["reaction_information", "check_all_appropriate", "life_threatening"]))
        m["8-3"] = self.chk(self._jbool(payload, ["reaction_information", "check_all_appropriate", "involved_or_prolonged_inpatient_hospitalization"]))
        m["8-4"] = self.chk(self._jbool(payload, ["reaction_information", "check_all_appropriate", "results_in_persistent_or_significant_disability_incapacity"]))
        m["8-5"] = self.chk(self._jbool(payload, ["reaction_information", "check_all_appropriate", "congenital_anomaly"]))
        m["8-6"] = self.chk(self._jbool(payload, ["reaction_information", "check_all_appropriate", "other_medically_important_condition"]))

        # II. 可疑药物信息
        m["14"] = self._jstr(payload, ["suspect_drug_information", "suspect_drug"])
        m["14续"] = self.cont_mark(self._jbool(payload, ["suspect_drug_information", "suspect_drug", "continued"]))

        m["15"] = self._jstr(payload, ["suspect_drug_information", "daily_dose"])
        m["15续"] = self.cont_mark(self._jbool(payload, ["suspect_drug_information", "daily_dose", "continued"]))

        m["16"] = self._jstr(payload, ["suspect_drug_information", "route_of_administration"])
        m["16续"] = self.cont_mark(self._jbool(payload, ["suspect_drug_information", "route_of_administration", "continued"]))

        m["17"] = self._jstr(payload, ["suspect_drug_information", "indication_for_use"])
        m["17续"] = self.cont_mark(self._jbool(payload, ["suspect_drug_information", "indication_for_use", "continued"]))

        m["18"] = self._jstr(payload, ["suspect_drug_information", "therapy_date"])
        m["19"] = self._jstr(payload, ["suspect_drug_information", "therapy_duration"])

        # 20-x （abate after stopping）
        m["20-1"] = self.chk(self._jbool(payload, ["suspect_drug_information", "abate_after_stopping", "yes"]))
        m["20-2"] = self.chk(self._jbool(payload, ["suspect_drug_information", "abate_after_stopping", "no"]))
        m["20-3"] = self.chk(self._jbool(payload, ["suspect_drug_information", "abate_after_stopping", "na"]))

        # 21-x （reappear after reintroduction）
        m["21-1"] = self.chk(self._jbool(payload, ["suspect_drug_information", "reappear_after_reintroduction", "yes"]))
        m["21-2"] = self.chk(self._jbool(payload, ["suspect_drug_information", "reappear_after_reintroduction", "no"]))
        m["21-3"] = self.chk(self._jbool(payload, ["suspect_drug_information", "reappear_after_reintroduction", "na"]))

        # III. 合并用药及病史
        m["22"] = self._jstr(payload, ["concomitant_drug_and_history", "concomitant_drug_and_dates_of_administration"])
        m["22续"] = self.cont_mark(self._jbool(payload, ["concomitant_drug_and_history", "concomitant_drug_and_dates_of_administration", "continued"]))

        m["23"] = self._jstr(payload, ["concomitant_drug_and_history", "other_relevant_history"])
        m["23续"] = self.cont_mark(self._jbool(payload, ["concomitant_drug_and_history", "other_relevant_history", "continued"]))

        # IV. 生产商信息
        m["24b"] = self._jstr(payload, ["manufacturer_information", "mfr_control_no"])
        m["24c"] = self._jstr(payload, ["manufacturer_information", "date_received_by_manufacturer"])
        m["报告日期"] = self._jstr(payload, ["manufacturer_information", "date_of_this_report"])

        # 24-x （report nullified）
        m["24-1"] = self.chk(self._jbool(payload, ["manufacturer_information", "report_nullified", "yes"]))
        m["24-2"] = self.chk(self._jbool(payload, ["manufacturer_information", "report_nullified", "no"]))

        # 24d-x （report source）
        m["24d-1"] = self.chk(self._jbool(payload, ["manufacturer_information", "report_source", "study"]))
        m["24d-2"] = self.chk(self._jbool(payload, ["manufacturer_information", "report_source", "literature"]))
        m["24d-3"] = self.chk(self._jbool(payload, ["manufacturer_information", "report_source", "health_professional"]))

        # 25-x （report type）
        m["25-1"] = self.chk(self._jbool(payload, ["manufacturer_information", "report_type", "initial"]))
        m["25-2"] = self.chk(self._jbool(payload, ["manufacturer_information", "report_type", "followup"]))

        # 研究信息
        m["研究名称"] = self._jstr(payload, ["manufacturer_information", "study_information", "study_name"])
        m["EudraCT编号"] = self._jstr(payload, ["manufacturer_information", "study_information", "eudract_number"])
        m["方案编号"] = self._jstr(payload, ["manufacturer_information", "study_information", "protocol_no"])
        m["中心编号"] = self._jstr(payload, ["manufacturer_information", "study_information", "center_no"])

        subj_id = self._jstr(payload, ["manufacturer_information", "study_information", "subject_id"])
        m["受试者 id"] = subj_id
        m["受试者id"] = subj_id  # 两种占位符都支持

        return m

    def _build_placeholder_map_appendix(self, payload: dict) -> Dict[str, str]:
        """附页（中文键）占位符映射"""
        m = {}
        m["国家续表"] = self._jstr(payload, ["country"])
        m["事件描述"] = self._jstr(payload, ["event_description"])
        # ⬇️ 新增：反应描述（续表）
        # 反应描述（续表）：list -> 文本；空列表 -> 空字符串
        desc = payload.get("appendix_describe_reaction")

        def _list_to_text(v):
            if not v:
                return ""
            if isinstance(v, (list, tuple)):
                out = []
                for it in v:
                    if isinstance(it, dict):
                        val = (
                            it.get("value") or it.get("text") or it.get("content") or ""
                        )
                    else:
                        val = str(it)
                    val = self._sanitize_free_text(str(val))
                    if val.strip() and val.strip() != "[]":
                        out.append(val.strip())
                return "\n".join(out)
            return self._sanitize_free_text(str(v))

        m["反应描述续表"] = _list_to_text(desc)
        m["研究类型"] = self._jstr(payload, ["study_type"])
        m["研究分期"] = self._jstr(payload, ["study_phase"])
        m["方案标题"] = self._jstr(payload, ["protocol_title"])
        m["药物使用时间记录，病史，合并治疗，用药及因果关系"] = self._jstr(
            payload, ["medication_history_causality_summary"]
        )
        m["报告者评论"] = self._jstr(payload, ["reporter_comment"])
        m["更新记录"] = self._jstr(payload, ["tracking_of_changes"])
        m["报告者给出的反应描述"] = self._jstr(
            payload, ["reaction_description_as_per_reporter"]
        )
        m["药物警戒评论"] = self._jstr(payload, ["pharmacovigilance_comments"])
        m["日剂量"] = self._jstr(payload, ["daily_dose"])
        m["其他相关病史"] = self._sanitize_free_text(self._jstr(payload, ["other_relevant_history"]))

        m["病史和并发疾病"] = self._jstr(
            payload, ["medical_history_and_concurrent_conditions"]
        )
        m["研究信息"]   = self._sanitize_free_text(self._jstr(payload, ["study_information"]))

        # === 改动点：按条目自身的 index 编号，而不是 1..n ===
        items = payload.get("lab_results_text") or []

        def _to_int_or_none(v):
            try:
                return int(str(v).strip())
            except Exception:
                return None

        # 先把每条记录的 index 提取出来；有 index 的按 index 升序排，没 index 的保持原相对顺序并放在最后
        items_with_idx = [(it, _to_int_or_none(it.get("index"))) for it in items]
        order = sorted(
            range(len(items_with_idx)),
            key=lambda i: (
                items_with_idx[i][1] is None,  # 无 index 的排后
                items_with_idx[i][1]
                if items_with_idx[i][1] is not None
                else i,  # 有 index 的按数值；无 index 的保持原顺序
            ),
        )

        blocks = []
        # 对于没有 index 的条目，仍然给一个后备顺序号（只用于这些无 index 的条目）
        fallback_seq = 0
        for pos in order:
            it, idx = items_with_idx[pos]
            name = str(it.get("test_name", "") or "")
            res = str(it.get("result_unstructured_data", "") or "")
            dt = str(it.get("test_date", "") or "")

            # 使用 index 作为前缀编号；若缺失或无法解析为整数，则用后备顺序号
            if idx is None:
                fallback_seq += 1
                prefix = f"{fallback_seq}）"
            else:
                prefix = f"{idx}）"

            block = f"{prefix}检查名称：{name}\n结果非结构化数据（自由文本）：{res}\n检查日期：{dt}"
            blocks.append(block)

        m["检查结果-文本"] = "\n".join(blocks)
        return m

    def build_placeholder_map(self, payload: dict) -> Dict[str, str]:
        """统一：合并首页与附页两类占位符"""
        m = {}
        m.update(self._build_placeholder_map_home(payload))
        m.update(self._build_placeholder_map_appendix(payload))
        return m

    # ============================= 动态表格：具体填充 =============================
    @staticmethod
    def _ensure_solid_borders(tbl: etree._Element):
        """给表格加实线边框（top/left/bottom/right/insideH/insideV）"""
        tblPr = tbl.find("./" + w("tblPr"))
        if tblPr is None:
            tblPr = etree.SubElement(tbl, w("tblPr"))
        borders = tblPr.find("./" + w("tblBorders"))
        if borders is None:
            borders = etree.SubElement(tblPr, w("tblBorders"))

        def set_edge(name: str):
            e = borders.find("./" + w(name)) or etree.SubElement(borders, w(name))
            e.set(w("val"), "single")
            e.set(w("sz"), "4")
            e.set(w("space"), "0")
            e.set(w("color"), "auto")

        for edge in ("top", "left", "bottom", "right", "insideH", "insideV"):
            set_edge(edge)

    @staticmethod
    def _clone_tcPr_width(tc_template: etree._Element) -> etree._Element:
        """克隆模板单元格的宽度与边框到数据单元格（避免复制表头填充/阴影等样式）"""
        tcPr_src = tc_template.find("./" + w("tcPr"))
        tcPr = etree.Element(w("tcPr"))
        if tcPr_src is not None:
            tcW = tcPr_src.find("./" + w("tcW"))
            if tcW is not None:
                tcPr.append(etree.fromstring(etree.tostring(tcW)))
            tcBorders = tcPr_src.find("./" + w("tcBorders"))
            if tcBorders is not None:
                tcPr.append(etree.fromstring(etree.tostring(tcBorders)))
        return tcPr

    @staticmethod
    def _clear_data_rows_keep_header(tbl: etree._Element):
        """清空数据行，保留表头（首行）"""
        rows = tbl.findall("./" + w("tr"))
        if len(rows) <= 1:
            return
        for tr in rows[1:]:
            tbl.remove(tr)

    def fill_lab_results_table(self, root: etree._Element, payload: dict) -> bool:
        """填充“实验室检查结果”表：表头为 检查名称 / 检查日期 / 检查结果 / 正常值"""
        # ==== 新增：无数据时直接删除“标题+表格”并返回 ====
        data = (payload or {}).get("lab_results_table") or []
        if not data:
            anchor = self._find_para_by_text(root, "实验室检查结果")
            if anchor is not None:
                try:
                    container, anchor_top, old_tbl, hold, idx, in_sdt = self._locate_container_and_tbl(anchor)
                    if old_tbl is not None:
                        try:
                            hold.remove(old_tbl)
                            self._log("【实验室检查结果】无数据：已删除标题后旧表。")
                        except Exception as _:
                            pass
                    parent = anchor_top.getparent()
                    if parent is not None:
                        parent.remove(anchor_top)
                        self._log("【实验室检查结果】无数据：已删除“实验室检查结果”标题段。")
                    return True
                except Exception as _:
                    pass
            # 找不到标题就尝试直接删表
            tbl_try = self._locate_lab_results_table(root)
            if tbl_try is not None and tbl_try.getparent() is not None:
                try:
                    tbl_try.getparent().remove(tbl_try)
                    self._log("【实验室检查结果】无数据：仅删除了表格。")
                    return True
                except Exception as _:
                    pass
            self._log("【实验室检查结果】无数据：未找到可删除的标题或表格。")
            return False
        # ==== 新增段落结束 ====
        tbl = self._locate_lab_results_table(root)
        if tbl is None:
            self._log("【实验室检查结果】未找到（请检查模板表头或 sdt 名称）。")
            return False

        self._log("【实验室检查结果】定位成功，清空旧数据并写入新数据。")
        self._ensure_solid_borders(tbl)
        self._clear_data_rows_keep_header(tbl)

        header_tr = tbl.find("./" + w("tr"))
        header_cells = header_tr.findall("./" + w("tc"))
        if len(header_cells) < 4:
            self._log("【实验室检查结果】表头异常：列数少于 4。")
            return False

        data = payload.get("lab_results_table") or []
        for row in data:
            tr = etree.Element(w("tr"))
            self._set_tr_height(tr, 278)

            # 1) 检查名称
            tc1 = etree.Element(w("tc"))
            tc1.append(self._clone_tcPr_width(header_cells[0]))
            tc1.append(
                self._make_p(self._strip_ocr_leading_colon(row.get("test_name", "")))
            )

            tr.append(tc1)

            # 2) 检查日期
            tc2 = etree.Element(w("tc"))
            tc2.append(self._clone_tcPr_width(header_cells[1]))
            tc2.append(
                self._make_p(self._strip_ocr_leading_colon(row.get("test_date", "")))
            )

            tr.append(tc2)

            # 3) 检查结果
            tc3 = etree.Element(w("tc"))
            tc3.append(self._clone_tcPr_width(header_cells[2]))
            tc3.append(
                self._make_p(self._strip_ocr_leading_colon(row.get("test_result", "")))
            )

            tr.append(tc3)

            # 4) 正常值
            tc4 = etree.Element(w("tc"))
            tc4.append(self._clone_tcPr_width(header_cells[3]))
            tc4.append(
                self._make_p(self._strip_ocr_leading_colon(row.get("normal_value", "")))
            )
            tr.append(tc4)

            tbl.append(tr)

        self._log(f"【实验室检查结果】完成：写入 {len(data)} 行。")
        return True

    # ---- 产品-反应层级（药物信息/因果/批准/标示） -------------------------------------
    @staticmethod
    def _mk_tbl_pr_clean() -> etree._Element:
        """构建一个干净的表格属性块（固定宽度、固定布局、单元边距等）"""
        tblPr = etree.Element(w("tblPr"))
        tOverlap = etree.SubElement(tblPr, w("tblOverlap"))
        tOverlap.set(w("val"), "never")
        tW = etree.SubElement(tblPr, w("tblW"))
        tW.set(w("w"), "5000")
        tW.set(w("type"), "pct")
        tLayout = etree.SubElement(tblPr, w("tblLayout"))
        tLayout.set(w("type"), "fixed")
        tCellMar = etree.SubElement(tblPr, w("tblCellMar"))
        left = etree.SubElement(tCellMar, w("left"))
        left.set(w("w"), "107")
        left.set(w("type"), "dxa")
        right = etree.SubElement(tCellMar, w("right"))
        right.set(w("w"), "107")
        right.set(w("type"), "dxa")
        tLook = etree.SubElement(tblPr, w("tblLook"))
        tLook.set(w("val"), "04A0")
        tLook.set(w("firstRow"), "1")
        tLook.set(w("lastRow"), "0")
        tLook.set(w("firstColumn"), "1")
        tLook.set(w("lastColumn"), "0")
        tLook.set(w("noHBand"), "0")
        tLook.set(w("noVBand"), "1")
        return tblPr

    @staticmethod
    def _mk_tbl_grid_30_35_35() -> etree._Element:
        """3 列网格：30% / 35% / 35%"""
        grid = etree.Element(w("tblGrid"))
        for twip in ("2708", "3159", "3159"):
            col = etree.SubElement(grid, w("gridCol"))
            col.set(w("w"), twip)
        return grid

    def _mk_tc(self, tr: etree._Element, pct_width: int, text: str = "", gridspan: int = None) -> etree._Element:
        """在 tr 上添加一个单元格（%宽度），并写入文本"""
        tc = etree.SubElement(tr, w("tc"))
        tcPr = etree.SubElement(tc, w("tcPr"))
        tcW = etree.SubElement(tcPr, w("tcW"))
        tcW.set(w("w"), str(pct_width))
        tcW.set(w("type"), "pct")
        if gridspan and int(gridspan) > 1:
            gs = etree.SubElement(tcPr, w("gridSpan"))
            gs.set(w("val"), str(gridspan))
        shd = etree.SubElement(tcPr, w("shd"))
        shd.set(w("val"), "clear")
        shd.set(w("color"), "auto")
        shd.set(w("fill"), "auto")
        tc.append(self._make_p(text or ""))
        return tc
    def fill_product_reaction_level(self, root: etree._Element, payload: dict) -> bool:
        """填充“药物反应水平”。当无数据时，删除“可疑药物（续）/药物反应水平/预设表格”等整块。"""
        items = (payload or {}).get("product_reaction_level") or []

        # === 无数据：整块删除 ===
        if not isinstance(items, list) or len(items) == 0:
            self._log("【药物反应水平】无数据：删除标题/空段/预设表格/相关 SDT。")

            # 0) 若模板中存在以 tag/alias 命名的 SDT，则直接移除整个 SDT
            for sdt in root.findall(".//" + w("sdt")):
                if self._sdt_has_name(sdt, "product_reaction_level"):
                    par = sdt.getparent()
                    if par is not None:
                        try:
                            par.remove(sdt)
                            self._log(
                                "【药物反应水平】已删除带有名称 product_reaction_level 的 SDT 容器。"
                            )
                        except Exception:
                            pass
                    break  # 足够了，通常只有一个

            # 1) 找到“药物反应水平”标题段（可能在/不在 SDT 内）
            anchor = self._find_para_by_text(
                root, "药物反应水平"
            ) or self._find_para_by_text(root, "药物反应水平和")
            if anchor is None:
                self._log("【药物反应水平】无数据且未找到标题段；跳过。")
                return True

            # 2) 锚点归位到容器顶层节点（以便正确删除其相邻兄弟）
            sdtc = anchor.xpath("ancestor::w:sdtContent[1]", namespaces=NS)
            container = sdtc[0] if sdtc else root.find(".//" + w("body"))
            anchor_top = self._top_level_child_in_container(anchor, container)

            # 3) 先记录“可疑药物（续）”所在节点（在 SDT 内则去 SDT 之前找，在 SDT 外则取 anchor 前兄弟）
            suspect_prev_node = None
            if sdtc:
                sdt_node = sdtc[0].getparent()  # <w:sdt>
                suspect_prev_node = sdt_node.getprevious()
            else:
                suspect_prev_node = anchor_top.getprevious()

            # 4) 删除标题后紧邻的“预设表格/空白段/SDT 整块”（直到遇到非空段且非表/SDT）
            sib = anchor_top.getnext()
            while sib is not None:
                if sib.tag == w("p") and self._is_empty_paragraph(sib):
                    nxt = sib.getnext()
                    try:
                        container.remove(sib)
                    except Exception:
                        break
                    sib = nxt
                    continue
                if sib.tag == w("tbl"):
                    nxt = sib.getnext()
                    try:
                        container.remove(sib)
                        self._log("【药物反应水平】已删除标题后的预设表格。")
                    except Exception:
                        break
                    sib = nxt
                    continue
                if sib.tag == w("sdt"):
                    # 标题后若紧跟一个 SDT（通常就是预设块），整块删掉
                    nxt = sib.getnext()
                    try:
                        container.remove(sib)
                        self._log("【药物反应水平】已删除标题后的 SDT 容器。")
                    except Exception:
                        break
                    sib = nxt
                    continue
                break  # 碰到其它内容就停

            # 5) 删除“药物反应水平”标题段本身
            try:
                container.remove(anchor_top)
            except Exception:
                pass

            # 6) 删除 SDT 外侧紧邻空白段（若 anchor 在 SDT 内）
            if sdtc:
                sdt_node = sdtc[0].getparent()
                parent_of_sdt = sdt_node.getparent()
                after = sdt_node.getnext()
                while (
                    after is not None
                    and after.tag == w("p")
                    and self._is_empty_paragraph(after)
                ):
                    nxt = after.getnext()
                    try:
                        parent_of_sdt.remove(after)
                    except Exception:
                        break
                    after = nxt

            # 7) 删除“可疑药物（续）”标题（若存在且紧邻）
            def _is_suspect_heading(p_el: etree._Element) -> bool:
                if p_el is None or p_el.tag != w("p"):
                    return False
                txt = self._text(p_el) or ""
                txt_norm = self._norm(txt)
                return (
                    ("可疑药物续" in txt_norm)
                    or ("可疑药物（续）" in txt)
                    or (txt.strip() == "可疑药物（续）")
                )

            # 在 SDT 场景下，删除 sdt 之前的“可疑药物（续）”；非 SDT 场景删除 anchor 前一个
            if sdtc:
                sdt_node = sdtc[0].getparent()
                parent_of_sdt = sdt_node.getparent()
                prev = sdt_node.getprevious()
                # 连带清掉紧邻的空白段
                while (
                    prev is not None
                    and prev.tag == w("p")
                    and (self._is_empty_paragraph(prev) or _is_suspect_heading(prev))
                ):
                    to_remove = prev
                    prev = prev.getprevious()
                    try:
                        parent_of_sdt.remove(to_remove)
                    except Exception:
                        break
            else:
                prev = suspect_prev_node
                while (
                    prev is not None
                    and prev.tag == w("p")
                    and (self._is_empty_paragraph(prev) or _is_suspect_heading(prev))
                ):
                    to_remove = prev
                    prev = prev.getprevious()
                    try:
                        container.remove(to_remove)
                    except Exception:
                        break

            self._log("【药物反应水平】无数据：已移除相关标题/表格/空段。")
            return True

        # === 有数据：正常渲染（含“药物：”编号递增与固定中文键） ===
        anchor_p = self._find_para_by_text(
            root, "药物反应水平"
        ) or self._find_para_by_text(root, "药物反应水平和")
        if anchor_p is None:
            self._log("【药物反应水平】未找到标题段落，跳过本节。")
            return False

        clean = lambda x: self._strip_ocr_leading_colon(self._s(x))
        new_tbls = []

        try:
            for idx, prl in enumerate(items, 1):
                if not isinstance(prl, dict):
                    continue
                di = prl.get("drug_information") or {}
                appr = prl.get("approval_information") or {}
                caus = prl.get("causality") or []
                labels = prl.get("labeling") or []

                tbl = etree.Element(w("tbl"))
                tbl.append(self._mk_tbl_pr_clean())

                def add_kv(label: str, value: str):
                    tr = etree.SubElement(tbl, w("tr"))
                    self._set_tr_height(tr, 278)
                    self._mk_tc(tr, 1500, label)
                    self._mk_tc(tr, 3500, clean(value))

                def add_full(text: str):
                    tr = etree.SubElement(tbl, w("tr"))
                    self._set_tr_height(tr, 278)
                    self._mk_tc(tr, 5000, clean(text))

                def add_pair_50_50(left_text: str, right_text: str):
                    tr = etree.SubElement(tbl, w("tr"))
                    self._set_tr_height(tr, 278)
                    self._mk_tc(tr, 2500, clean(left_text))
                    self._mk_tc(tr, 2500, clean(right_text))

                # —— 编号药物 ——（1）2）…）
                add_kv(f"{idx}）药物：", di.get("name", ""))

                # —— 固定中文键 ——
                add_kv("活性物质：", di.get("active_substance", ""))
                add_kv("编码类别：", di.get("coding_class", ""))
                add_kv("药物表征：", di.get("drug_characterization", ""))
                add_kv("剂型：", di.get("form_of_admin", ""))
                add_kv("批号：", di.get("lot_number", ""))
                add_kv("日剂量：", di.get("daily_dose", ""))
                add_kv("给药途径：", di.get("route_of_admin", ""))
                add_kv("适应症：", di.get("indications", ""))

                # —— 治疗日期（三列）
                ttext = clean(di.get("therapy_dates", ""))
                prefix, start_val, end_val = self._parse_from_to(ttext)
                start_cn = self._s(self._norm_cn_date(start_val))
                end_cn = self._s(self._norm_cn_date(end_val))

                tr = etree.SubElement(tbl, w("tr"))
                self._set_tr_height(tr, 278)
                self._mk_tc(tr, 1500, "治疗日期：")
                self._mk_tc(
                    tr,
                    1750,
                    f"{self._s(prefix)}开始日期：{start_cn}"
                    if start_cn
                    else f"{self._s(prefix)}开始日期：",
                )
                self._mk_tc(tr, 1750, f"结束日期：{end_cn}" if end_cn else "结束日期：")

                add_kv("治疗持续时间：", di.get("therapy_duration", ""))
                add_kv("对药物采取的措施：", di.get("action_taken_with_drug", ""))

                # —— 因果关系 ——
                if caus:
                    add_kv("因果关系", "")
                    for j, c in enumerate(caus, 1):
                        term = (c.get("reaction_term", "") or "").strip()
                        if term:
                            add_full(f"{j}）{term}")
                        add_kv(
                            "报告者因果关系评估：",
                            c.get("causality_as_per_reporter", ""),
                        )
                        mfr = (
                            c.get("causality_as_per_mfr")
                            or c.get("causality_as_per_sponsor")
                            or c.get("causality_as_per_mfr_sponsor")
                            or ""
                        )
                        add_kv("生产商因果关系评估：", mfr)
                        add_kv("去激发：", c.get("dechallenge", ""))

                # —— 批准信息 ——（含小节标题）
                if appr and any(
                    str(appr.get(k, "")).strip()
                    for k in (
                        "authorization_number",
                        "authorization_country",
                        "approval_type",
                    )
                ):
                    add_kv("批准信息：", "")
                    if appr.get("authorization_number"):
                        add_kv("1）授权编号：", appr.get("authorization_number", ""))
                    if appr.get("authorization_country"):
                        add_kv("授权国家/地区：", appr.get("authorization_country", ""))
                    if appr.get("approval_type"):
                        add_kv("批准类型：", appr.get("approval_type", ""))

                # —— 标示 ——
                if labels:
                    add_kv("标示：", "")
                    for k, lab in enumerate(labels, 1):
                        term = (lab.get("reaction_term", "") or "").strip()
                        if term:
                            add_full(f"{k}）{term}")
                        status_raw = lab.get("labeling_status", "")
                        left_txt, right_txt = self._map_labeling_status_to_cn(
                            status_raw
                        )
                        add_pair_50_50(left_txt, right_txt)

                new_tbls.append(tbl)

        except Exception as e:
            self._log(f"【药物反应水平】构建新表出错：{e}（已跳过，不改动文档）")
            return False

        if not new_tbls:
            self._log("【药物反应水平】没有可插入的表行，跳过。")
            return False

        # 清掉标题后已有的旧表，再顺序插入新表
        parent = anchor_p.getparent()
        sib = anchor_p.getnext()
        while sib is not None and sib.tag == w("tbl"):
            nxt = sib.getnext()
            parent.remove(sib)
            sib = nxt

        after = anchor_p
        for tbl in new_tbls:
            after.addnext(tbl)
            after = tbl

        self._log(
            f"【药物反应水平】完成：插入 {len(new_tbls)} 张表（已按 1）2）… 编号“药物：”）。"
        )
        return True

    # ---- 合并用药 --------------------------------------------------------------
    @staticmethod
    def _find_list_by_keys(obj, keys: set) -> Optional[list]:
        """递归在 payload 中寻找“可能的合并用药列表键”"""
        if isinstance(obj, dict):
            for k, v in obj.items():
                if k in keys and isinstance(v, list):
                    return v
                got = CiomsDocxFiller._find_list_by_keys(v, keys)
                if got is not None:
                    return got
        elif isinstance(obj, list):
            for it in obj:
                got = CiomsDocxFiller._find_list_by_keys(it, keys)
                if got is not None:
                    return got
        return None

    def _get_concomitant_list(self, payload: dict) -> List[dict]:
        """
        从 payload 中抽取“合并用药”列表，兼容多种字段名：
        - concomitant_drugs / concomitant_medications / conmeds / concomitants
        """
        candidates = {"concomitant_drugs", "concomitant_medications", "conmeds", "concomitants"}
        if not isinstance(payload, dict):
            return []
        for k in candidates:
            v = payload.get(k)
            if isinstance(v, list):
                return [x for x in v if isinstance(x, dict)]
        v = self._find_list_by_keys(payload, candidates)
        return [x for x in v if isinstance(x, dict)] if isinstance(v, list) else []

    def _build_concomitant_tbl(self, drugs: List[dict]) -> etree._Element:
        """构建“合并用药”表"""
        tbl = etree.Element(w("tbl"))
        tbl.append(self._mk_tbl_pr_clean())
        tbl.append(self._mk_tbl_grid_30_35_35())
        clean = self._strip_ocr_leading_colon  # <== 新增

        def add_row_30_70(label_text: str, value_text: str):
            tr = etree.SubElement(tbl, w("tr"))
            self._set_tr_height(tr, 278)
            self._mk_tc(tr, 1500, label_text)
            self._mk_tc(tr, 3500, (value_text or ""), 2)

        def add_row_therapy_dates(raw_value: str):
            prefix, start_cn, end_cn = self._parse_from_to(raw_value)
            tr = etree.SubElement(tbl, w("tr"))
            self._set_tr_height(tr, 278)
            self._mk_tc(tr, 1500, "治疗日期：")
            self._mk_tc(tr, 1750, f"{prefix}开始日期：{start_cn}")
            self._mk_tc(tr, 1750, f"结束日期：{end_cn}")

        LABELS = {
            "active_substance": "活性物质：",
            "form_strength":    "规格：",
            "form_of_admin":    "给药方式：",
            "daily_dose":       "日剂量：",
            "indications":      "适应症：",
            "therapy_dates":    "治疗日期：",
            "dosage_text":      "剂量文本：",
        }
        ORDER = ["active_substance", "form_strength", "form_of_admin", "daily_dose",
                 "indications", "therapy_dates", "dosage_text"]

        row_count = 0
        for i, d in enumerate(drugs, 1):
            # 1) 药物名（有就渲染，没就跳过，不影响后续字段）
            dn = clean(d.get("drug_name") or "")
            if dn:
                add_row_30_70(f"{i}）药物：", dn)
                row_count += 1

            # 2) 其余字段
            for key in ORDER:
                val = d.get(key)
                if not val:
                    continue
                if key == "therapy_dates":
                    raw = clean(val)
                    prefix, start_val, end_val = self._parse_from_to(raw)
                    start_cn = self._s(self._norm_cn_date(start_val))
                    end_cn = self._s(self._norm_cn_date(end_val))

                    tr = etree.SubElement(tbl, w("tr"))
                    self._set_tr_height(tr, 278)
                    self._mk_tc(tr, 1500, "治疗日期：")
                    self._mk_tc(
                        tr,
                        1750,
                        f"{self._s(prefix)}开始日期：{start_cn}"
                        if start_cn
                        else f"{self._s(prefix)}开始日期：",
                    )
                    self._mk_tc(
                        tr, 1750, f"结束日期：{end_cn}" if end_cn else "结束日期："
                    )
                else:
                    add_row_30_70(LABELS[key], clean(val))
                row_count += 1

        if row_count == 0:
            tr = etree.SubElement(tbl, w("tr"))
            self._mk_tc(tr, 5000, "（无合并用药信息）", gridspan=3)
        return tbl

    def fill_concomitant_drugs(self, root: etree._Element, payload: dict) -> bool:
        """填充“合并用药”表；当无数据时，删除标题段与紧随其后的表格。"""
        drugs = self._get_concomitant_list(payload)
        self._log(f"【合并用药】待填充数据：{len(drugs)} 条")

        anchor = self._find_para_by_text(root, "合并用药（续）")
        if anchor is None:
            anchor = self._find_para_by_text(root, "合并用药")
        if anchor is None:
            self._log("【合并用药】未找到段落标题“合并用药（续）/合并用药”，跳过本节。")
            return False

        # === 无数据：删除标题段与其后的表格 ===
        if not drugs:
            container, anchor_top, old_tbl, hold, idx, in_sdt = (
                self._locate_container_and_tbl(anchor)
            )
            if old_tbl is not None:
                try:
                    hold.remove(old_tbl)
                    self._log("【合并用药】无数据：已删除紧随标题后的旧表。")
                except Exception as e:
                    self._log(f"【合并用药】删除旧表失败：{e}")
            # 删除标题段（顶级定位，避免只删内层节点）
            try:
                parent = anchor_top.getparent()
                if parent is not None:
                    parent.remove(anchor_top)
                    self._log(
                        "【合并用药】无数据：已删除“合并用药（续）/合并用药”标题段。"
                    )
            except Exception as e:
                self._log(f"【合并用药】删除标题段失败：{e}")
            return True

        # === 有数据：正常渲染表格 ===
        container, anchor_top, old_tbl, hold, idx, in_sdt = (
            self._locate_container_and_tbl(anchor)
        )
        self._log(
            "【合并用药】定位完成："
            f"{'SDT 区域内' if in_sdt else '正文区域'}；"
            f"{'已在原位置替换旧表' if old_tbl is not None else '标题后插入新表'}。"
        )

        new_tbl = self._build_concomitant_tbl(drugs)

        # 插入或替换
        if old_tbl is not None:
            pos = idx
            hold.remove(old_tbl)
            hold.insert(pos, new_tbl)
        else:
            hold.insert(idx, new_tbl)

        # —— 清理空白段（关键：就放在插入/替换之后）——
        # 1) 清理表格后的空段（避免：表格 -> 空行 -> “23.其他相关病史（续）”）
        sib = new_tbl.getnext()
        removed_after = 0
        while sib is not None and sib.tag == w("p") and self._is_empty_paragraph(sib):
            nxt = sib.getnext()
            try:
                hold.remove(sib)
                removed_after += 1
            except Exception:
                break
            sib = nxt
        if removed_after:
            self._log(f"【合并用药】表后清理空白段：{removed_after} 行。")

        # 2) 清理表格前紧挨着的空段（避免：标题 -> 空行 -> 表格）
        sib_prev = new_tbl.getprevious()
        removed_before = 0
        while (
            sib_prev is not None
            and sib_prev.tag == w("p")
            and self._is_empty_paragraph(sib_prev)
        ):
            try:
                hold.remove(sib_prev)
                removed_before += 1
            except Exception:
                break
            sib_prev = new_tbl.getprevious()
        if removed_before:
            self._log(f"【合并用药】表前清理空白段：{removed_before} 行。")

        rows_rendered = len(new_tbl.findall("./" + w("tr")))
        # —— 额外：如果表在 SDT 内，还要清理 SDT 外紧随其后的空白段 ——
        if in_sdt:
            sdt_node = hold.getparent()  # <w:sdt>
            parent_of_sdt = sdt_node.getparent()  # 通常是 <w:body> 或上层容器
            # 删掉 <w:sdt> 之后连续出现的空段（含仅有编号/书签但无文本的段）
            sib = sdt_node.getnext()
            removed_outside = 0
            while (
                sib is not None and sib.tag == w("p") and self._is_empty_paragraph(sib)
            ):
                nxt = sib.getnext()
                try:
                    parent_of_sdt.remove(sib)
                    removed_outside += 1
                except Exception:
                    break
                sib = nxt
            if removed_outside:
                self._log(f"【合并用药】清理 SDT 外的空白段：{removed_outside} 行。")
        extra_removed = self._trim_gap_before_history_heading(root)
        if extra_removed:
            self._log(
                f"【合并用药】→【其他相关病史】之间额外兜底清理空白段：{extra_removed} 行。"
            )
        self._log(
            f"【合并用药】完成：共渲染 {rows_rendered} 行。\n"
            f"布局：\n"
            f"  - “治疗日期”行：三列（30% / 35% / 35%）。\n"
            f"  - 其他行：两列（左 30%，右 70%——右单元格跨两列）。"
        )
        return True

    # ---- 其他研究注册号 ----------------------------------------------------------
    def _locate_other_reg_tbl(self, root: etree._Element) -> Optional[etree._Element]:
        """定位“其他研究注册号”表：优先 SDT 名称，否则按表头包含“注册号/注册国家/地区”"""
        tbl = self._find_tbl_by_sdt_name(root, "other_study_registration_numbers")
        if tbl is None:
            tbl = self._find_tbl_by_header_texts(root, ["注册号", "注册国家", "地区"])
        return tbl

    def _map_labeling_status_to_cn(self, status_raw: str) -> Tuple[str, str]:
        """
        将 labeling_status 归一化为 (left, right)：
        - left: "IB"（如果文本里出现 IB），否则 ""
        - right: "标示" / "未标示" / ""（根据多语言、多写法判断）
        """
        # 安全清洗
        s = "" if status_raw is None else str(status_raw)
        s = re.sub(r"[\u200b\u200c\u200d\uFEFF]", "", s)  # 去零宽
        s = s.replace("：", ":")
        s = re.sub(r"\s+", "", s)  # 去空白
        sl = s.lower()

        # 左侧：是否包含 IB
        left = "IB" if "ib" in sl else ""

        # 右侧：先判否定，再判肯定
        neg_tokens = [
            "未标示",
            "未標示",
            "未标注",
            "未標注",
            "未标记",
            "未標記",
            "不标示",
            "不標示",
            "不标注",
            "不標注",
            "不标记",
            "不標記",
            "未包含",
            "不包含",
            "未列入",
            "未纳入",
            "未納入",
            "notlabel",
            "notlabelled",
            "notlabeled",
            "unlabelled",
            "unlabeled",
            "notlisted",
            "unlisted",
            "no",
        ]
        pos_tokens = [
            "标示",
            "標示",
            "已标示",
            "已標示",
            "标注",
            "標注",
            "已标注",
            "已標注",
            "已标记",
            "已標記",
            "标记的",
            "標記的",
            "有标示",
            "有標示",
            "有标注",
            "有標注",
            "列入",
            "纳入",
            "納入",
            "包含",
            "labeled",
            "labelled",
            "listed",
            "included",
            "contains",
            "yes",
        ]

        def _has_any(txt: str, toks: list) -> bool:
            tl = txt.lower()
            for t in toks:
                if t.replace(" ", "").lower() in tl:
                    return True
            return False

        if _has_any(s, neg_tokens):
            right = "未标示"
        elif _has_any(s, pos_tokens):
            right = "标示"
        else:
            right = ""

        return left, right
    def fill_other_registrations(self, root: etree._Element, payload: dict) -> bool:
        regs = (payload or {}).get("other_study_registration_numbers") or []
        pasts = (payload or {}).get("past_therapies") or []
        if not regs and not pasts:
            return False

        # 1) 锚点：标题段（尽量宽松）
        anchor = None
        for kw in ("其他研究注册号（续）", "24.其他研究注册号", "其他研究注册号"):
            anchor = self._find_para_by_text(root, kw)
            if anchor is not None:
                break
        if anchor is None:
            self._log("【其他研究注册号】未找到标题；放弃。")
            return False

        # 2) 构造新表（严格对齐“正常 XML”的网格与写法）
        tbl = etree.Element(w("tbl"))

        # tblPr：沿用你全局风格；额外加上 tblStyle(5) 与 tblInd=0（与示例一致）
        tblPr = etree.SubElement(tbl, w("tblPr"))
        tblStyle = etree.SubElement(tblPr, w("tblStyle"))
        tblStyle.set(w("val"), "5")
        tblW = etree.SubElement(tblPr, w("tblW"))
        tblW.set(w("w"), "5000")
        tblW.set(w("type"), "pct")
        tblInd = etree.SubElement(tblPr, w("tblInd"))
        tblInd.set(w("w"), "0")
        tblInd.set(w("type"), "dxa")
        tblLayout = etree.SubElement(tblPr, w("tblLayout"))
        tblLayout.set(w("type"), "fixed")
        cellMar = etree.SubElement(tblPr, w("tblCellMar"))
        for edge, wval in (
            ("top", "0"),
            ("left", "107"),
            ("bottom", "0"),
            ("right", "107"),
        ):
            e = etree.SubElement(cellMar, w(edge))
            e.set(w("w"), wval)
            e.set(w("type"), "dxa")

        # grid：== 3080 / 1540 / 4620 ==
        grid = etree.SubElement(tbl, w("tblGrid"))
        for tw in ("3080", "1540", "4620"):
            col = etree.SubElement(grid, w("gridCol"))
            col.set(w("w"), tw)

        def _new_tr() -> etree._Element:
            tr = etree.Element(w("tr"))
            # 行高 atLeast（与样例一致，避免被 Word 压缩后触发列折叠）
            trPr = etree.SubElement(tr, w("trPr"))
            trHeight = etree.SubElement(trPr, w("trHeight"))
            trHeight.set(w("val"), "278")
            trHeight.set(w("hRule"), "atLeast")
            return tr

        def _tc_pct(
            width_pct: int, text: str = "", gridspan: int = None
        ) -> etree._Element:
            tc = etree.Element(w("tc"))
            tcPr = etree.SubElement(tc, w("tcPr"))
            tcW = etree.SubElement(tcPr, w("tcW"))
            tcW.set(w("w"), str(width_pct))  # 5000=100%
            tcW.set(w("type"), "pct")
            if gridspan:
                gs = etree.SubElement(tcPr, w("gridSpan"))
                gs.set(w("val"), str(gridspan))
            shd = etree.SubElement(tcPr, w("shd"))
            shd.set(w("val"), "clear")
            shd.set(w("color"), "auto")
            shd.set(w("fill"), "FFFFFF")  # 与样例一致
            # 段落（保持你的字体/间距）
            tc.append(self._make_p(*(self._ascii_cn_runs(text))))
            return tc

        def _append_tr(*tcs: etree._Element):
            tr = _new_tr()
            for tc in tcs:
                tr.append(tc)
            tbl.append(tr)

        # 3) 头部（50/50）：左跨两列 | 右第三列
        _append_tr(_tc_pct(2500, "注册号", gridspan=2), _tc_pct(2500, "注册国家/地区"))

        # 4) 注册号数据（50/50）：左跨两列 | 右第三列
        wrote_reg = False
        for it in regs:
            reg = (it.get("registration_number") or "").strip()
            country = (it.get("registration_country") or "").strip()
            if not reg and not country:
                continue
            _append_tr(_tc_pct(2500, reg, gridspan=2), _tc_pct(2500, country))
            wrote_reg = True

        # 5) 既往治疗（30/70）：标题行 + KV 明细
        def _add_kv_30_70(label: str, value: str):
            # 左：1666（≈1/3）；右：3333（≈2/3，跨两列）
            _append_tr(
                _tc_pct(1666, label),  # 用第1列（1/3 ≈ 30%）
                _tc_pct(3333, value, gridspan=2),  # 用第2+3列（2/3 ≈ 70%）
            )

        if pasts:
            # 小节标题：左跨两列“既往治疗” | 右空
            _append_tr(_tc_pct(2500, "既往治疗", gridspan=2), _tc_pct(2500, ""))
            for p in pasts:
                name = (p.get("product_name") or "").strip()
                ind = (p.get("indication") or "").strip()
                sd = self._norm_cn_date(p.get("start_date") or "")
                ed = self._norm_cn_date(p.get("stop_date") or "")
                if name:
                    _add_kv_30_70("产品名称：", name)
                if ind:
                    _add_kv_30_70("适应症：", ind)
                if sd:
                    _add_kv_30_70("开始日期：", sd)
                if ed:
                    _add_kv_30_70("结束日期：", ed)

        if self.debug:
            cols = [
                c.get(w("w"))
                for c in tbl.findall(".//" + w("tblGrid") + "/" + w("gridCol"))
            ]
            self._log(
                f"【其他研究注册号】新表 grid 列宽={cols}（应为 ['3080','1540','4620']）"
            )

        # 6) 用标题为锚：删除紧随其后的第一张旧表（及其前的空段），再插入新表
        sdtc = anchor.xpath("ancestor::w:sdtContent[1]", namespaces=NS)
        container = sdtc[0] if sdtc else root.find(".//" + w("body"))
        anchor_top = self._top_level_child_in_container(anchor, container)

        sib = anchor_top.getnext()
        # 跳过标题后的空段
        while sib is not None and sib.tag == w("p") and self._is_empty_paragraph(sib):
            nxt = sib.getnext()
            try:
                container.remove(sib)
            except Exception:
                pass
            sib = nxt
        # 删除紧随其后的第一张旧表
        if sib is not None and sib.tag == w("tbl"):
            try:
                container.remove(sib)
                self._log("【其他研究注册号】已删除标题后第一张旧表。")
            except Exception as e:
                self._log(f"【其他研究注册号】删除旧表失败：{e}")

        # 插入新表
        idx = container.index(anchor_top)
        container.insert(idx + 1, tbl)
        self._log(
            "【其他研究注册号/既往治疗】已在标题后插入新表（grid=3080/1540/4620；注册号50/50；既往治疗≈30/70）。"
        )
        return True

    # ============================= 调试工具（中文日志） =============================
    def debug_list_sdt_names(self, root: etree._Element):
        """打印模板中所有 SDT 的 tag/alias（用于模板配置排查）"""
        names = []
        for sdt in root.findall(".//" + w("sdt")):
            pr = sdt.find(w("sdtPr"))
            if pr is None:
                continue
            tag = pr.find(w("tag"))
            alias = pr.find(w("alias"))
            names.append((
                tag.get(w("val")) if tag is not None else None,
                alias.get(w("val")) if alias is not None else None
            ))
        self._log("【模板检查】发现以下内容控件：")
        for t, a in names:
            self._log(f"  - 标记(tag)={t or '无'}，别名(alias)={a or '无'}")

    def debug_locate_all(self, root: etree._Element):
        """中文说明：模板里哪些区域可被定位（不代表一定填充）"""
        has_lab = self._locate_lab_results_table(root) is not None
        self._log(f"【定位检查】实验室检查结果表：{'已找到' if has_lab else '未找到'}")

        prl_sdt = self._find_tbl_by_sdt_name(root, "product_reaction_level") is not None
        prl_anchor = (self._find_para_by_text(root, "药物反应水平") is not None
                      or self._find_para_by_text(root, "药物反应水平和") is not None)
        self._log(f"【定位检查】药物反应水平：内容控件={'有' if prl_sdt else '无'}；段落标题定位={'可用' if prl_anchor else '不可用'}")

        cm_sdt = self._find_tbl_by_sdt_name(root, "concomitant_drugs") is not None
        cm_anchor = (self._find_para_by_text(root, "合并用药（续）") is not None
                     or self._find_para_by_text(root, "合并用药") is not None)
        self._log(f"【定位检查】合并用药：内容控件={'有' if cm_sdt else '无'}；段落标题定位={'可用' if cm_anchor else '不可用'}")

        other_ok = self._locate_other_reg_tbl(root) is not None
        self._log(f"【定位检查】其他研究注册号表：{'已找到' if other_ok else '未找到'}")

    # ============================= 单个部件处理与整包处理 =============================
    def _process_xml_part(
        self,
        xml_bytes: bytes,
        placeholder_map: Dict[str, str],
        payload: Optional[dict],
        part_name: str
    ) -> bytes:
        """
        处理单个 XML 部件：
        - 属性与段落文本占位符替换
        - 统一拆段落（\n → 新段落）
        - 主文档（document.xml）专属：四类动态表格填充
        - 统一 Wingdings 转换（在拆段落之后进行）
        - 清理 tblpPr
        """
        if not xml_bytes.strip().startswith(b"<"):
            return xml_bytes

        parser = etree.XMLParser(remove_blank_text=False)
        root = etree.fromstring(xml_bytes, parser=parser)

        # 1) 属性占位符替换（含 w:sym 字体修正）
        self._replace_in_attributes(root, placeholder_map)

        # 2) 段落文本占位符替换（仅写入文本与 \n；不在此时转 w:sym）
        for p in root.xpath(".//w:p", namespaces=NS):
            self._replace_in_paragraph(p, placeholder_map)

        # 3) 将含 \n 的段落拆成多个段落
        self._split_paragraphs_with_newlines(root)
        # === 新增：根据空值裁剪成块（删除标题+空占位段） ===
        self._prune_empty_sections(root, placeholder_map, payload)
        # 4) 主文档：动态表格
        if payload is not None and part_name == "word/document.xml":
            if self.debug:
                self._log("【阶段】处理主文档（word/document.xml）")
                self.debug_list_sdt_names(root)
                self.debug_locate_all(root)

            try:
                self.fill_lab_results_table(root, payload)
            except Exception as e:
                self._log(f"【实验室检查结果】填充出错：{e}")

            try:
                ok = self.fill_product_reaction_level(root, payload)
                self._log(f"【药物反应水平】处理结果：{'已填充' if ok else '已跳过'}")
            except Exception as e:
                self._log(f"【药物反应水平】填充出错：{e}")

            try:
                ok_cm = self.fill_concomitant_drugs(root, payload)
                self._log(f"【合并用药】处理结果：{'已填充' if ok_cm else '已跳过'}")
            except Exception as e:
                self._log(f"【合并用药】填充出错：{e}")

            try:
                self.fill_other_registrations(root, payload)
            except Exception as e:
                self._log(f"【其他研究注册号】填充出错：{e}")
        try:
            removed = self._trim_gap_before_history_heading(root)
            if removed:
                self._log(f"【兜底】已清理“其他相关病史”标题前空白段：{removed} 行。")
        except Exception as e:
            self._log(f"【兜底】清理“其他相关病史”标题前空白段失败：{e}")
        # 5) 在整个部件上进行 Wingdings 代码→<w:sym> 的最终转换
        self._convert_all_wingdings(root)

        # 6) 移除所有 tblpPr，避免表格浮动
        self._strip_all_tblpPr(root)

        return etree.tostring(root, xml_declaration=True, encoding="UTF-8", standalone="yes")

    def fill_docx_placeholders(self, template_path: str, output_path: str, payload: dict) -> str:
        """
        处理整个 DOCX：
        - 解压 DOCX（zip）
        - 主文档/页眉/页脚：做占位符替换（主文档同时做动态表格）
        - 其他文件原样复制
        """
        placeholders = self.build_placeholder_map(payload)
        with ZipFile(template_path, "r") as zin, ZipFile(output_path, "w", compression=ZIP_DEFLATED) as zout:
            for item in zin.infolist():
                data = zin.read(item.filename)
                is_main = item.filename == "word/document.xml"
                is_header = item.filename.startswith("word/header") and item.filename.endswith(".xml")
                is_footer = item.filename.startswith("word/footer") and item.filename.endswith(".xml")

                if is_main or is_header or is_footer:
                    try:
                        new_data = self._process_xml_part(
                            data,
                            placeholders,
                            payload if is_main else None,
                            part_name=item.filename
                        )
                        zout.writestr(item, new_data)
                    except Exception as e:
                        # 安全回退：写回原始数据
                        self._log(f"【处理异常】{item.filename}：{e}（已回退写入原数据）")
                        zout.writestr(item, data)
                else:
                    zout.writestr(item, data)

        return output_path

    def fill_docx(self, template_path: str, payload: dict, output_path: str) -> str:
        """
        简化的DOCX填充方法，直接接受payload字典

        Args:
            template_path: 模板文件路径
            payload: 数据字典
            output_path: 输出文件路径

        Returns:
            输出文件路径
        """
        return self.fill_docx_placeholders(template_path, output_path, payload)


# ----------------------------- CLI 封装 -----------------------------
def fill_docx_from_json(
    template: str,
    json_path: str,
    out_path: str,
    debug: bool = False
):
    """使用 JSON 填充 DOCX 模板"""
    with open(json_path, "r", encoding="utf-8") as f:
        payload = json.load(f)

    os.makedirs(os.path.dirname(os.path.abspath(out_path)) or ".", exist_ok=True)

    filler = CiomsDocxFiller(enable_debug=debug)
    filler.fill_docx_placeholders(template, out_path, payload)

    app_logger.info(f"✅ 已生成：{out_path}")


import datetime

def main():
    # 请将以下路径替换为您自己的文件路径
    template_file = "/Users/<USER>/PycharmProjects/cioms/data/模板/模板.docx"
    json_file = "/Users/<USER>/PycharmProjects/cioms/data/中文翻译结果.json"

    # 用年月日时分秒命名输出文件
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    output_file = f"{timestamp}.docx"

    fill_docx_from_json(template_file, json_file, output_file, debug=True)


if __name__ == "__main__":
    main()
